/**
 * @file 服务注册器 - service-registry.js
 * @description 管理系统所有服务的注册和配置，实现依赖注入迁移
 * @version v1.0.0
 * <AUTHOR> Order Processing System
 * @date 2025-01-07
 */

/**
 * @class ServiceRegistry
 * @description 服务注册器，负责注册所有系统服务到DI容器
 */
class ServiceRegistry {
    constructor(diContainer) {
        /** @type {DIContainer} DI容器实例 */
        this.container = diContainer;
        
        /** @type {Logger} 日志记录器 */
        this.logger = window.logger || console;
        
        /** @type {string} 注册器版本 */
        this.version = 'v1.0.0';
        
        /** @type {Set<string>} 已注册的服务集合 */
        this.registeredServices = new Set();
        
        this.logger.info('ServiceRegistry', `服务注册器初始化完成 ${this.version}`);
    }

    /**
     * @function registerCoreServices - 注册核心服务
     * @description 注册系统核心服务，包括配置、日志、状态管理等
     */
    registerCoreServices() {
        try {
            // 1. 配置服务 - 最基础的服务，无依赖
            this.container.registerInstance('config', window.SYSTEM_CONFIG || {}, {
                tags: ['core', 'config'],
                metadata: { description: '系统全局配置' }
            });

            // 2. 日志服务 - 依赖配置服务
            this.container.registerSingleton('logger', (config) => {
                return window.logger || window.Logger ? new window.Logger(config) : console;
            }, {
                dependencies: ['config'],
                tags: ['core', 'logging'],
                metadata: { description: '系统日志服务' }
            });

            // 3. 应用状态服务 - 依赖日志服务
            this.container.registerSingleton('appState', (logger) => {
                if (window.AppState) {
                    return window.appState || new window.AppState();
                }
                logger.warn('ServiceRegistry', 'AppState类未找到，使用空对象');
                return {};
            }, {
                dependencies: ['logger'],
                tags: ['core', 'state'],
                metadata: { description: '应用状态管理' }
            });

            // 4. 事件总线服务 - 实现发布订阅模式
            this.container.registerSingleton('eventBus', () => {
                return this.createEventBus();
            }, {
                tags: ['core', 'events'],
                metadata: { description: '事件总线服务' }
            });

            // 5. 性能监控服务
            this.container.registerSingleton('performanceMonitor', (logger) => {
                return window.performanceMonitor || window.PerformanceMonitor ? 
                    new window.PerformanceMonitor() : null;
            }, {
                dependencies: ['logger'],
                tags: ['core', 'performance'],
                metadata: { description: '性能监控服务' }
            });

            this.registeredServices.add('core');
            this.logger.info('ServiceRegistry', '核心服务注册完成', {
                services: ['config', 'logger', 'appState', 'eventBus', 'performanceMonitor']
            });

        } catch (error) {
            this.logger.error('ServiceRegistry', '核心服务注册失败', error);
            throw error;
        }
    }

    /**
     * @function registerAPIServices - 注册API服务
     * @description 注册所有API相关服务
     */
    registerAPIServices() {
        try {
            // 1. HTTP客户端服务
            this.container.registerSingleton('httpClient', (config, logger) => {
                return {
                    request: async (url, options = {}) => {
                        logger.debug('HttpClient', `请求: ${options.method || 'GET'} ${url}`);
                        return fetch(url, options);
                    }
                };
            }, {
                dependencies: ['config', 'logger'],
                tags: ['api', 'http'],
                metadata: { description: 'HTTP客户端服务' }
            });

            // 2. API服务 - 依赖HTTP客户端和会话管理
            this.container.registerSingleton('apiService', (httpClient, sessionManager, logger, appState) => {
                if (window.ApiService) {
                    return window.ApiService.instance || new window.ApiService();
                }
                logger.warn('ServiceRegistry', 'ApiService类未找到');
                return null;
            }, {
                dependencies: ['httpClient', 'sessionManager', 'logger', 'appState'],
                tags: ['api', 'business'],
                metadata: { description: 'GoMyHire API服务' }
            });

            // 3. 会话管理服务
            this.container.registerSingleton('sessionManager', (logger) => {
                if (window.SessionManager) {
                    return new window.SessionManager();
                }
                logger.warn('ServiceRegistry', 'SessionManager类未找到');
                return null;
            }, {
                dependencies: ['logger'],
                tags: ['api', 'session'],
                metadata: { description: '用户会话管理' }
            });

            this.registeredServices.add('api');
            this.logger.info('ServiceRegistry', 'API服务注册完成', {
                services: ['httpClient', 'apiService', 'sessionManager']
            });

        } catch (error) {
            this.logger.error('ServiceRegistry', 'API服务注册失败', error);
            throw error;
        }
    }

    /**
     * @function registerAIServices - 注册AI服务
     * @description 注册所有AI相关服务
     */
    registerAIServices() {
        try {
            // 1. LLM服务 - 管理DeepSeek和Gemini
            this.container.registerSingleton('llmService', (config, logger) => {
                if (window.LLMService) {
                    return window.llmService || new window.LLMService();
                }
                logger.warn('ServiceRegistry', 'LLMService类未找到');
                return null;
            }, {
                dependencies: ['config', 'logger'],
                tags: ['ai', 'llm'],
                metadata: { description: 'LLM服务管理器' }
            });

            // 2. 图像服务 - Google Vision API
            this.container.registerSingleton('imageService', (config, logger) => {
                if (window.ImageService) {
                    return window.imageService || new window.ImageService();
                }
                logger.warn('ServiceRegistry', 'ImageService类未找到');
                return null;
            }, {
                dependencies: ['config', 'logger'],
                tags: ['ai', 'vision'],
                metadata: { description: '图像处理服务' }
            });

            // 3. 订单解析服务
            this.container.registerSingleton('orderParser', (llmService, imageService, logger) => {
                if (window.OrderParser) {
                    return window.orderParser || new window.OrderParser();
                }
                logger.warn('ServiceRegistry', 'OrderParser类未找到');
                return null;
            }, {
                dependencies: ['llmService', 'imageService', 'logger'],
                tags: ['ai', 'parsing'],
                metadata: { description: '订单解析服务' }
            });

            this.registeredServices.add('ai');
            this.logger.info('ServiceRegistry', 'AI服务注册完成', {
                services: ['llmService', 'imageService', 'orderParser']
            });

        } catch (error) {
            this.logger.error('ServiceRegistry', 'AI服务注册失败', error);
            throw error;
        }
    }

    /**
     * @function registerBusinessServices - 注册业务服务
     * @description 注册智能选择、订单管理等业务服务
     */
    registerBusinessServices() {
        try {
            // 1. 智能选择服务
            this.container.registerSingleton('smartSelection', (appState, logger) => {
                if (window.SmartSelectionService) {
                    return window.smartSelection || new window.SmartSelectionService();
                }
                logger.warn('ServiceRegistry', 'SmartSelectionService类未找到');
                return null;
            }, {
                dependencies: ['appState', 'logger'],
                tags: ['business', 'intelligence'],
                metadata: { description: '智能选择服务' }
            });

            // 2. 订单管理服务
            this.container.registerSingleton('orderManager', (smartSelection, apiService, logger) => {
                if (window.OrderManager) {
                    return window.OrderManager.instance || new window.OrderManager();
                }
                logger.warn('ServiceRegistry', 'OrderManager类未找到');
                return null;
            }, {
                dependencies: ['smartSelection', 'apiService', 'logger'],
                tags: ['business', 'orders'],
                metadata: { description: '订单管理服务' }
            });

            // 3. OTA配置管理服务
            this.container.registerSingleton('otaProfileManager', (appState, logger) => {
                if (window.OTAProfileManager) {
                    return window.otaProfileManager || new window.OTAProfileManager(appState);
                }
                logger.warn('ServiceRegistry', 'OTAProfileManager类未找到');
                return null;
            }, {
                dependencies: ['appState', 'logger'],
                tags: ['business', 'profiles'],
                metadata: { description: 'OTA配置管理' }
            });

            // 4. 地址搜索服务
            this.container.registerSingleton('addressSearchService', (config, logger) => {
                if (window.AddressSearchService) {
                    return window.addressSearchService || new window.AddressSearchService();
                }
                logger.warn('ServiceRegistry', 'AddressSearchService类未找到');
                return null;
            }, {
                dependencies: ['config', 'logger'],
                tags: ['business', 'address'],
                metadata: { description: '地址搜索服务' }
            });

            this.registeredServices.add('business');
            this.logger.info('ServiceRegistry', '业务服务注册完成', {
                services: ['smartSelection', 'orderManager', 'otaProfileManager', 'addressSearchService']
            });

        } catch (error) {
            this.logger.error('ServiceRegistry', '业务服务注册失败', error);
            throw error;
        }
    }

    /**
     * @function registerUIServices - 注册UI服务
     * @description 注册界面控制、通知等UI服务
     */
    registerUIServices() {
        try {
            // 1. 界面控制器服务
            this.container.registerSingleton('interfaceController', (eventBus, logger) => {
                if (window.InterfaceController) {
                    return new window.InterfaceController();
                }
                logger.warn('ServiceRegistry', 'InterfaceController类未找到');
                return null;
            }, {
                dependencies: ['eventBus', 'logger'],
                tags: ['ui', 'controller'],
                metadata: { description: '界面控制器' }
            });

            // 2. 通知管理服务
            this.container.registerSingleton('notificationManager', (logger) => {
                return window.notificationManager || {
                    success: (title, message) => logger.info('Notification', `${title}: ${message}`),
                    error: (title, message) => logger.error('Notification', `${title}: ${message}`),
                    warning: (title, message) => logger.warn('Notification', `${title}: ${message}`),
                    info: (title, message) => logger.info('Notification', `${title}: ${message}`)
                };
            }, {
                dependencies: ['logger'],
                tags: ['ui', 'notification'],
                metadata: { description: '通知管理服务' }
            });

            // 3. 登录处理器服务
            this.container.registerSingleton('loginHandler', (apiService, notificationManager, logger) => {
                if (window.EnhancedLoginHandler) {
                    return window.enhancedLoginHandler || new window.EnhancedLoginHandler();
                }
                logger.warn('ServiceRegistry', 'EnhancedLoginHandler类未找到');
                return null;
            }, {
                dependencies: ['apiService', 'notificationManager', 'logger'],
                tags: ['ui', 'auth'],
                metadata: { description: '登录处理器' }
            });

            this.registeredServices.add('ui');
            this.logger.info('ServiceRegistry', 'UI服务注册完成', {
                services: ['interfaceController', 'notificationManager', 'loginHandler']
            });

        } catch (error) {
            this.logger.error('ServiceRegistry', 'UI服务注册失败', error);
            throw error;
        }
    }

    /**
     * @function registerUtilityServices - 注册工具服务
     * @description 注册模块加载器、性能优化器等工具服务
     */
    registerUtilityServices() {
        try {
            // 1. 模块加载器服务
            this.container.registerSingleton('moduleLoader', (logger) => {
                if (window.ModuleLoader) {
                    return window.moduleLoader || new window.ModuleLoader();
                }
                logger.warn('ServiceRegistry', 'ModuleLoader类未找到');
                return null;
            }, {
                dependencies: ['logger'],
                tags: ['utility', 'loader'],
                metadata: { description: '模块加载器' }
            });

            // 2. 性能优化器服务
            this.container.registerSingleton('performanceOptimizer', (performanceMonitor, logger) => {
                if (window.PerformanceOptimizer) {
                    return new window.PerformanceOptimizer();
                }
                logger.warn('ServiceRegistry', 'PerformanceOptimizer类未找到');
                return null;
            }, {
                dependencies: ['performanceMonitor', 'logger'],
                tags: ['utility', 'performance'],
                metadata: { description: '性能优化器' }
            });

            // 3. 弹性管理器服务
            this.container.registerSingleton('resilienceManager', (logger) => {
                if (window.ResilienceManager) {
                    return new window.ResilienceManager();
                }
                logger.warn('ServiceRegistry', 'ResilienceManager类未找到');
                return null;
            }, {
                dependencies: ['logger'],
                tags: ['utility', 'resilience'],
                metadata: { description: '系统弹性管理器' }
            });

            this.registeredServices.add('utility');
            this.logger.info('ServiceRegistry', '工具服务注册完成', {
                services: ['moduleLoader', 'performanceOptimizer', 'resilienceManager']
            });

        } catch (error) {
            this.logger.error('ServiceRegistry', '工具服务注册失败', error);
            throw error;
        }
    }

    /**
     * @function registerAllServices - 注册所有服务
     * @description 按照依赖顺序注册所有系统服务
     */
    async registerAllServices() {
        const startTime = Date.now();
        
        try {
            this.logger.info('ServiceRegistry', '开始注册所有服务...');

            // 按照依赖顺序注册服务
            this.registerCoreServices();
            await this.waitForServices(['config', 'logger', 'appState']);

            this.registerAPIServices();
            await this.waitForServices(['sessionManager', 'apiService']);

            this.registerAIServices();
            await this.waitForServices(['llmService', 'imageService']);

            this.registerBusinessServices();
            await this.waitForServices(['smartSelection', 'orderManager']);

            this.registerUIServices();
            await this.waitForServices(['interfaceController', 'notificationManager']);

            this.registerUtilityServices();

            // 验证所有依赖关系
            const validationResult = this.container.validateDependencies();
            if (!validationResult.valid) {
                this.logger.error('ServiceRegistry', '依赖验证失败', validationResult.errors);
                throw new Error('服务依赖验证失败');
            }

            const endTime = Date.now();
            const stats = this.container.getStatistics();
            
            this.logger.info('ServiceRegistry', '所有服务注册完成', {
                totalTime: `${endTime - startTime}ms`,
                registeredCategories: Array.from(this.registeredServices),
                ...stats
            });

            return true;

        } catch (error) {
            this.logger.error('ServiceRegistry', '服务注册失败', error);
            throw error;
        }
    }

    /**
     * @function waitForServices - 等待服务可用
     * @private
     * @param {string[]} serviceNames - 服务名称数组
     * @param {number} timeout - 超时时间（毫秒）
     * @returns {Promise<void>}
     */
    async waitForServices(serviceNames, timeout = 5000) {
        const startTime = Date.now();
        
        while (Date.now() - startTime < timeout) {
            const allAvailable = serviceNames.every(name => {
                try {
                    this.container.resolve(name);
                    return true;
                } catch {
                    return false;
                }
            });

            if (allAvailable) {
                return;
            }

            await new Promise(resolve => setTimeout(resolve, 10));
        }

        throw new Error(`服务等待超时: ${serviceNames.join(', ')}`);
    }

    /**
     * @function createEventBus - 创建事件总线
     * @private
     * @returns {EventBus} 事件总线实例
     */
    createEventBus() {
        return {
            events: new Map(),
            
            on(event, callback) {
                if (!this.events.has(event)) {
                    this.events.set(event, []);
                }
                this.events.get(event).push(callback);
            },
            
            off(event, callback) {
                if (this.events.has(event)) {
                    const callbacks = this.events.get(event);
                    const index = callbacks.indexOf(callback);
                    if (index > -1) {
                        callbacks.splice(index, 1);
                    }
                }
            },
            
            emit(event, data) {
                if (this.events.has(event)) {
                    this.events.get(event).forEach(callback => {
                        try {
                            callback(data);
                        } catch (error) {
                            console.error('EventBus回调执行失败:', error);
                        }
                    });
                }
            },
            
            once(event, callback) {
                const onceWrapper = (data) => {
                    callback(data);
                    this.off(event, onceWrapper);
                };
                this.on(event, onceWrapper);
            }
        };
    }

    /**
     * @function getRegisteredCategories - 获取已注册的服务分类
     * @returns {string[]} 服务分类数组
     */
    getRegisteredCategories() {
        return Array.from(this.registeredServices);
    }

    /**
     * @function generateServiceReport - 生成服务注册报告
     * @returns {ServiceReport} 服务报告
     */
    generateServiceReport() {
        const stats = this.container.getStatistics();
        const dependencyGraph = this.container.getDependencyGraph();
        const validationResult = this.container.validateDependencies();

        return {
            registry: {
                version: this.version,
                categories: this.getRegisteredCategories(),
                registrationTime: new Date().toISOString()
            },
            container: stats,
            validation: validationResult,
            dependencyGraph,
            services: this.container.getRegisteredServices().map(name => ({
                name,
                info: this.container.getServiceInfo(name)
            }))
        };
    }
}

/**
 * @typedef {Object} ServiceReport
 * @property {Object} registry - 注册器信息
 * @property {ContainerStatistics} container - 容器统计
 * @property {ValidationResult} validation - 验证结果
 * @property {DependencyGraph} dependencyGraph - 依赖图
 * @property {Object[]} services - 服务信息数组
 */

// 导出到全局
if (typeof window !== 'undefined') {
    window.ServiceRegistry = ServiceRegistry;
    console.log('✅ 服务注册器已加载并可用');
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = ServiceRegistry;
}