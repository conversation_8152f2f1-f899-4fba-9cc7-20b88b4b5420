/**
 * @file 依赖注入容器
 * @description 实现轻量级依赖注入容器，支持服务注册、解析和单例管理
 * @version v1.0.0
 * <AUTHOR>
 * @created 2025-01-16
 */

/**
 * @class DIContainer - 依赖注入容器
 * @description 提供服务注册、依赖解析和生命周期管理功能
 * 
 * 主要特性:
 * - 服务注册与工厂模式
 * - 单例模式支持
 * - 依赖关系自动解析
 * - 循环依赖检测
 * - 类型安全的依赖注入
 * 
 * @example
 * // 基础用法
 * const container = new DIContainer();
 * 
 * // 注册服务
 * container.register('config', () => new ConfigManager(), { singleton: true });
 * container.register('logger', (config) => new Logger(config), {
 *     singleton: true,
 *     dependencies: ['config']
 * });
 * 
 * // 解析服务
 * const logger = container.resolve('logger');
 */
class DIContainer {
    /**
     * @function constructor - 构造函数
     * @description 初始化依赖注入容器
     */
    constructor() {
        /** @type {Map<string, Object>} 服务定义映射 */
        this.services = new Map();
        
        /** @type {Map<string, any>} 单例实例缓存 */
        this.instances = new Map();
        
        /** @type {Set<string>} 当前解析路径，用于循环依赖检测 */
        this.resolving = new Set();
        
        /** @type {string} 容器版本 */
        this.version = 'v1.0.0';
        
        // 内置日志功能
        this.logger = window.logger || console;
        
        this.logger.info('DIContainer', '依赖注入容器初始化完成', {
            version: this.version,
            timestamp: new Date().toISOString()
        });
    }

    // #region 服务注册管理

    /**
     * @function register - 注册服务
     * @description 向容器注册一个服务及其工厂函数
     * 
     * @param {string} name - 服务名称，必须唯一
     * @param {Function} factory - 服务工厂函数，用于创建服务实例
     * @param {Object} options - 注册选项
     * @param {boolean} [options.singleton=false] - 是否为单例模式
     * @param {string[]} [options.dependencies=[]] - 依赖的其他服务名称列表
     * @param {string} [options.description] - 服务描述信息
     * @param {string} [options.version] - 服务版本
     * 
     * @returns {DIContainer} 返回容器实例，支持链式调用
     * 
     * @throws {Error} 当服务名已存在或工厂函数无效时抛出错误
     * 
     * @example
     * // 注册单例配置服务
     * container.register('config', () => {
     *     return new ConfigManager();
     * }, {
     *     singleton: true,
     *     description: '系统配置管理器',
     *     version: 'v2.0.1'
     * });
     * 
     * // 注册依赖其他服务的服务
     * container.register('apiService', (config, logger) => {
     *     return new ApiService(config, logger);
     * }, {
     *     singleton: true,
     *     dependencies: ['config', 'logger'],
     *     description: 'API服务层'
     * });
     */
    register(name, factory, options = {}) {
        // 参数验证
        if (!name || typeof name !== 'string') {
            throw new Error('DIContainer: 服务名称必须是非空字符串');
        }
        
        if (typeof factory !== 'function') {
            throw new Error(`DIContainer: 服务 "${name}" 的工厂必须是函数`);
        }
        
        if (this.services.has(name)) {
            this.logger.warn('DIContainer', `服务 "${name}" 已存在，将被覆盖`);
        }

        // 处理选项
        const serviceDefinition = {
            name,
            factory,
            singleton: options.singleton || false,
            dependencies: options.dependencies || [],
            description: options.description || '',
            version: options.version || '1.0.0',
            createdAt: new Date().toISOString()
        };

        // 验证依赖关系
        this._validateDependencies(name, serviceDefinition.dependencies);

        // 注册服务
        this.services.set(name, serviceDefinition);

        this.logger.debug('DIContainer', `服务 "${name}" 注册成功`, {
            singleton: serviceDefinition.singleton,
            dependencies: serviceDefinition.dependencies,
            description: serviceDefinition.description
        });

        return this; // 支持链式调用
    }

    /**
     * @function _validateDependencies - 验证依赖关系
     * @description 检查依赖列表的有效性，防止循环依赖
     * @private
     * 
     * @param {string} serviceName - 当前服务名
     * @param {string[]} dependencies - 依赖列表
     * @throws {Error} 当发现循环依赖时抛出错误
     */
    _validateDependencies(serviceName, dependencies) {
        if (!Array.isArray(dependencies)) {
            throw new Error(`DIContainer: 服务 "${serviceName}" 的依赖必须是数组`);
        }

        // 检查是否存在自依赖
        if (dependencies.includes(serviceName)) {
            throw new Error(`DIContainer: 服务 "${serviceName}" 不能依赖自身`);
        }

        // TODO: 实现更复杂的循环依赖检测
        // 当前版本在解析时检测，后续可以在注册时预检测
    }

    // #endregion

    // #region 服务解析与实例化

    /**
     * @function resolve - 解析服务
     * @description 根据服务名称解析并返回服务实例
     * 
     * @param {string} name - 要解析的服务名称
     * @returns {any} 服务实例
     * 
     * @throws {Error} 当服务未注册或解析失败时抛出错误
     * 
     * @example
     * // 解析单个服务
     * const logger = container.resolve('logger');
     * 
     * // 解析带依赖的服务
     * const apiService = container.resolve('apiService');
     */
    resolve(name) {
        if (!name || typeof name !== 'string') {
            throw new Error('DIContainer: 服务名称必须是非空字符串');
        }

        // 检查服务是否已注册
        const serviceDefinition = this.services.get(name);
        if (!serviceDefinition) {
            throw new Error(`DIContainer: 服务 "${name}" 未注册`);
        }

        // 检查循环依赖
        if (this.resolving.has(name)) {
            const resolvingPath = Array.from(this.resolving).join(' -> ');
            throw new Error(`DIContainer: 检测到循环依赖: ${resolvingPath} -> ${name}`);
        }

        // 单例检查
        if (serviceDefinition.singleton && this.instances.has(name)) {
            this.logger.debug('DIContainer', `返回单例服务 "${name}"`);
            return this.instances.get(name);
        }

        // 开始解析
        this.resolving.add(name);

        try {
            // 解析依赖
            const resolvedDependencies = this._resolveDependencies(serviceDefinition.dependencies);
            
            // 创建实例
            const instance = this._createInstance(serviceDefinition, resolvedDependencies);
            
            // 缓存单例
            if (serviceDefinition.singleton) {
                this.instances.set(name, instance);
                this.logger.debug('DIContainer', `单例服务 "${name}" 已缓存`);
            }

            this.logger.debug('DIContainer', `服务 "${name}" 解析成功`, {
                singleton: serviceDefinition.singleton,
                dependencyCount: serviceDefinition.dependencies.length
            });

            return instance;

        } catch (error) {
            this.logger.error('DIContainer', `服务 "${name}" 解析失败`, {
                error: error.message,
                dependencies: serviceDefinition.dependencies
            });
            throw error;
        } finally {
            // 清理解析状态
            this.resolving.delete(name);
        }
    }

    /**
     * @function _resolveDependencies - 解析依赖关系
     * @description 递归解析服务的所有依赖
     * @private
     * 
     * @param {string[]} dependencies - 依赖服务名称列表
     * @returns {any[]} 解析后的依赖实例数组
     */
    _resolveDependencies(dependencies) {
        return dependencies.map(depName => {
            try {
                return this.resolve(depName);
            } catch (error) {
                throw new Error(`DIContainer: 解析依赖 "${depName}" 失败: ${error.message}`);
            }
        });
    }

    /**
     * @function _createInstance - 创建服务实例
     * @description 调用工厂函数创建服务实例
     * @private
     * 
     * @param {Object} serviceDefinition - 服务定义
     * @param {any[]} resolvedDependencies - 已解析的依赖实例
     * @returns {any} 创建的服务实例
     */
    _createInstance(serviceDefinition, resolvedDependencies) {
        try {
            const instance = serviceDefinition.factory(...resolvedDependencies);
            
            // 实例后处理
            if (instance && typeof instance === 'object') {
                // 为实例添加元数据（如果支持）
                if (instance.constructor && instance.constructor.name) {
                    this.logger.debug('DIContainer', `创建实例 "${serviceDefinition.name}"`, {
                        type: instance.constructor.name,
                        version: serviceDefinition.version
                    });
                }
            }
            
            return instance;
        } catch (error) {
            throw new Error(`DIContainer: 创建服务 "${serviceDefinition.name}" 实例失败: ${error.message}`);
        }
    }

    // #endregion

    // #region 容器管理与工具方法

    /**
     * @function has - 检查服务是否已注册
     * @description 检查指定名称的服务是否已在容器中注册
     * 
     * @param {string} name - 服务名称
     * @returns {boolean} 如果服务已注册返回true，否则返回false
     * 
     * @example
     * if (container.has('logger')) {
     *     const logger = container.resolve('logger');
     * }
     */
    has(name) {
        return this.services.has(name);
    }

    /**
     * @function remove - 移除服务
     * @description 从容器中移除指定的服务及其单例实例
     * 
     * @param {string} name - 要移除的服务名称
     * @returns {boolean} 如果成功移除返回true，否则返回false
     * 
     * @example
     * container.remove('oldService');
     */
    remove(name) {
        const removed = this.services.delete(name);
        if (removed) {
            this.instances.delete(name);
            this.logger.info('DIContainer', `服务 "${name}" 已移除`);
        }
        return removed;
    }

    /**
     * @function clear - 清空容器
     * @description 清除所有注册的服务和单例实例
     * 
     * @example
     * container.clear(); // 清空所有服务
     */
    clear() {
        const serviceCount = this.services.size;
        const instanceCount = this.instances.size;
        
        this.services.clear();
        this.instances.clear();
        this.resolving.clear();
        
        this.logger.info('DIContainer', '容器已清空', {
            removedServices: serviceCount,
            removedInstances: instanceCount
        });
    }

    /**
     * @function getRegisteredServices - 获取已注册的服务列表
     * @description 返回所有已注册服务的名称和基本信息
     * 
     * @returns {Array<Object>} 服务信息列表
     * 
     * @example
     * const services = container.getRegisteredServices();
     * console.log('已注册的服务:', services);
     */
    getRegisteredServices() {
        return Array.from(this.services.entries()).map(([name, definition]) => ({
            name,
            singleton: definition.singleton,
            dependencies: definition.dependencies,
            description: definition.description,
            version: definition.version,
            hasInstance: this.instances.has(name)
        }));
    }

    /**
     * @function getDependencyGraph - 获取依赖关系图
     * @description 返回所有服务的依赖关系图，用于调试和可视化
     * 
     * @returns {Object} 依赖关系图对象
     */
    getDependencyGraph() {
        const graph = {};
        
        for (const [name, definition] of this.services) {
            graph[name] = {
                dependencies: definition.dependencies,
                dependents: []
            };
        }
        
        // 计算反向依赖关系
        for (const [name, definition] of this.services) {
            definition.dependencies.forEach(depName => {
                if (graph[depName]) {
                    graph[depName].dependents.push(name);
                }
            });
        }
        
        return graph;
    }

    /**
     * @function validateContainer - 验证容器状态
     * @description 检查容器中所有服务的依赖关系是否有效
     * 
     * @returns {Object} 验证结果对象
     */
    validateContainer() {
        const result = {
            valid: true,
            errors: [],
            warnings: [],
            serviceCount: this.services.size,
            instanceCount: this.instances.size
        };

        // 检查依赖关系
        for (const [name, definition] of this.services) {
            definition.dependencies.forEach(depName => {
                if (!this.services.has(depName)) {
                    result.valid = false;
                    result.errors.push(`服务 "${name}" 依赖未注册的服务 "${depName}"`);
                }
            });
        }

        // 检查孤立的单例实例
        for (const instanceName of this.instances.keys()) {
            if (!this.services.has(instanceName)) {
                result.warnings.push(`发现孤立的单例实例 "${instanceName}"`);
            }
        }

        return result;
    }

    // #endregion

    // #region 向后兼容性支持

    /**
     * @function createCompatibilityAliases - 创建兼容性别名
     * @description 为已注册的服务在window对象上创建别名，保持向后兼容
     * 
     * @param {Object} aliasMap - 别名映射对象 {serviceName: windowPropertyName}
     * 
     * @example
     * container.createCompatibilityAliases({
     *     'apiService': 'apiService',
     *     'smartSelection': 'smartSelection'
     * });
     */
    createCompatibilityAliases(aliasMap) {
        for (const [serviceName, windowProperty] of Object.entries(aliasMap)) {
            if (this.has(serviceName)) {
                try {
                    const instance = this.resolve(serviceName);
                    window[windowProperty] = instance;
                    this.logger.debug('DIContainer', `创建兼容性别名 window.${windowProperty} -> ${serviceName}`);
                } catch (error) {
                    this.logger.error('DIContainer', `创建别名失败 ${windowProperty}`, error);
                }
            }
        }
    }

    // #endregion
}

// #region 全局导出和初始化

// 创建全局容器实例
if (!window.diContainer) {
    window.diContainer = new DIContainer();
    
    // 添加全局快捷方法
    window.registerService = (name, factory, options) => {
        return window.diContainer.register(name, factory, options);
    };
    
    window.resolveService = (name) => {
        return window.diContainer.resolve(name);
    };
    
    // 导出类定义
    window.DIContainer = DIContainer;
    
    // 版本信息
    const version = 'v1.0.0';
    
    if (window.logger) {
        window.logger.success('DIContainer', '依赖注入容器模块加载完成', {
            version,
            globalInstance: true,
            shortcuts: ['registerService', 'resolveService']
        });
    }
}

// #endregion

/* 
 * 使用示例和最佳实践:
 * 
 * 1. 基础服务注册:
 *    window.diContainer.register('config', () => new ConfigManager(), { singleton: true });
 * 
 * 2. 依赖注入:
 *    window.diContainer.register('logger', (config) => new Logger(config), {
 *        singleton: true,
 *        dependencies: ['config']
 *    });
 * 
 * 3. 服务解析:
 *    const logger = window.diContainer.resolve('logger');
 * 
 * 4. 向后兼容:
 *    window.diContainer.createCompatibilityAliases({
 *        'apiService': 'apiService'
 *    });
 * 
 * 5. 容器状态检查:
 *    const status = window.diContainer.validateContainer();
 *    console.log('容器状态:', status);
 */