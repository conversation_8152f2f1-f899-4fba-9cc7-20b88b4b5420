/**
 * @file 依赖注入容器 - dependency-injection-container.js
 * @description 实现依赖注入模式，管理模块依赖关系，减少全局变量使用
 * @version v1.0.0
 * <AUTHOR> Order Processing System
 * @date 2025-01-07
 */

/**
 * @class DIContainer
 * @description 依赖注入容器，管理服务的注册、解析和生命周期
 */
class DIContainer {
    constructor() {
        /** @type {Map<string, ServiceDefinition>} 服务定义映射 */
        this.services = new Map();
        
        /** @type {Map<string, any>} 单例实例缓存 */
        this.singletonInstances = new Map();
        
        /** @type {Set<string>} 正在解析的服务，用于检测循环依赖 */
        this.resolving = new Set();
        
        /** @type {Logger} 日志记录器 */
        this.logger = window.logger || console;
        
        /** @type {string} 容器版本 */
        this.version = 'v1.0.0';
        
        this.logger.info('DIContainer', `依赖注入容器初始化完成 ${this.version}`);
    }

    /**
     * @function register - 注册服务
     * @param {string} name - 服务名称
     * @param {Function|any} factory - 工厂函数或实例
     * @param {ServiceOptions} options - 服务选项
     * @returns {DIContainer} 链式调用支持
     */
    register(name, factory, options = {}) {
        const serviceDefinition = {
            name,
            factory,
            singleton: options.singleton || false,
            dependencies: options.dependencies || [],
            tags: options.tags || [],
            metadata: options.metadata || {},
            lazy: options.lazy || false
        };

        this.services.set(name, serviceDefinition);
        
        this.logger.debug('DIContainer', `服务已注册: ${name}`, {
            singleton: serviceDefinition.singleton,
            dependencies: serviceDefinition.dependencies,
            lazy: serviceDefinition.lazy
        });

        return this;
    }

    /**
     * @function registerSingleton - 注册单例服务
     * @param {string} name - 服务名称
     * @param {Function|any} factory - 工厂函数或实例
     * @param {ServiceOptions} options - 服务选项
     * @returns {DIContainer} 链式调用支持
     */
    registerSingleton(name, factory, options = {}) {
        return this.register(name, factory, { ...options, singleton: true });
    }

    /**
     * @function registerInstance - 注册实例
     * @param {string} name - 服务名称
     * @param {any} instance - 服务实例
     * @param {ServiceOptions} options - 服务选项
     * @returns {DIContainer} 链式调用支持
     */
    registerInstance(name, instance, options = {}) {
        this.singletonInstances.set(name, instance);
        return this.register(name, () => instance, { ...options, singleton: true });
    }

    /**
     * @function resolve - 解析服务
     * @param {string} name - 服务名称
     * @returns {any} 服务实例
     * @throws {Error} 服务未找到或循环依赖错误
     */
    resolve(name) {
        // 检查循环依赖
        if (this.resolving.has(name)) {
            const cycle = Array.from(this.resolving).join(' -> ') + ' -> ' + name;
            throw new Error(`检测到循环依赖: ${cycle}`);
        }

        // 获取服务定义
        const serviceDefinition = this.services.get(name);
        if (!serviceDefinition) {
            throw new Error(`服务未注册: ${name}`);
        }

        // 单例检查
        if (serviceDefinition.singleton && this.singletonInstances.has(name)) {
            return this.singletonInstances.get(name);
        }

        // 开始解析
        this.resolving.add(name);
        
        try {
            // 解析依赖
            const resolvedDependencies = serviceDefinition.dependencies.map(dep => {
                return this.resolve(dep);
            });

            // 创建实例
            let instance;
            if (typeof serviceDefinition.factory === 'function') {
                instance = serviceDefinition.factory(...resolvedDependencies);
            } else {
                instance = serviceDefinition.factory;
            }

            // 缓存单例
            if (serviceDefinition.singleton) {
                this.singletonInstances.set(name, instance);
            }

            this.logger.debug('DIContainer', `服务已解析: ${name}`, {
                singleton: serviceDefinition.singleton,
                dependencyCount: resolvedDependencies.length
            });

            return instance;

        } finally {
            // 清理解析状态
            this.resolving.delete(name);
        }
    }

    /**
     * @function resolveMany - 批量解析服务
     * @param {string[]} names - 服务名称数组
     * @returns {any[]} 服务实例数组
     */
    resolveMany(names) {
        return names.map(name => this.resolve(name));
    }

    /**
     * @function has - 检查服务是否已注册
     * @param {string} name - 服务名称
     * @returns {boolean} 是否已注册
     */
    has(name) {
        return this.services.has(name);
    }

    /**
     * @function unregister - 注销服务
     * @param {string} name - 服务名称
     * @returns {boolean} 是否成功注销
     */
    unregister(name) {
        const removed = this.services.delete(name);
        this.singletonInstances.delete(name);
        
        if (removed) {
            this.logger.debug('DIContainer', `服务已注销: ${name}`);
        }
        
        return removed;
    }

    /**
     * @function getServiceInfo - 获取服务信息
     * @param {string} name - 服务名称
     * @returns {ServiceDefinition|null} 服务定义
     */
    getServiceInfo(name) {
        return this.services.get(name) || null;
    }

    /**
     * @function getRegisteredServices - 获取所有已注册的服务名称
     * @returns {string[]} 服务名称数组
     */
    getRegisteredServices() {
        return Array.from(this.services.keys());
    }

    /**
     * @function getServicesByTag - 根据标签获取服务
     * @param {string} tag - 标签名称
     * @returns {string[]} 匹配的服务名称数组
     */
    getServicesByTag(tag) {
        const matchingServices = [];
        
        for (const [name, definition] of this.services) {
            if (definition.tags.includes(tag)) {
                matchingServices.push(name);
            }
        }
        
        return matchingServices;
    }

    /**
     * @function createScope - 创建作用域容器
     * @returns {ScopedDIContainer} 作用域容器实例
     */
    createScope() {
        return new ScopedDIContainer(this);
    }

    /**
     * @function validateDependencies - 验证依赖关系
     * @returns {ValidationResult} 验证结果
     */
    validateDependencies() {
        const errors = [];
        const warnings = [];

        for (const [name, definition] of this.services) {
            // 检查依赖是否都已注册
            for (const dep of definition.dependencies) {
                if (!this.has(dep)) {
                    errors.push(`服务 ${name} 依赖未注册的服务: ${dep}`);
                }
            }

            // 检查潜在的循环依赖
            try {
                this.detectCircularDependency(name, new Set());
            } catch (error) {
                errors.push(`服务 ${name}: ${error.message}`);
            }
        }

        return {
            valid: errors.length === 0,
            errors,
            warnings
        };
    }

    /**
     * @function detectCircularDependency - 检测循环依赖
     * @private
     * @param {string} serviceName - 服务名称
     * @param {Set<string>} visited - 已访问的服务
     * @param {Set<string>} recursionStack - 递归栈
     */
    detectCircularDependency(serviceName, visited, recursionStack = new Set()) {
        if (recursionStack.has(serviceName)) {
            throw new Error(`检测到循环依赖: ${Array.from(recursionStack).join(' -> ')} -> ${serviceName}`);
        }

        if (visited.has(serviceName)) {
            return;
        }

        const definition = this.services.get(serviceName);
        if (!definition) {
            return;
        }

        visited.add(serviceName);
        recursionStack.add(serviceName);

        for (const dep of definition.dependencies) {
            this.detectCircularDependency(dep, visited, recursionStack);
        }

        recursionStack.delete(serviceName);
    }

    /**
     * @function clear - 清空容器
     */
    clear() {
        this.services.clear();
        this.singletonInstances.clear();
        this.resolving.clear();
        
        this.logger.info('DIContainer', '容器已清空');
    }

    /**
     * @function getDependencyGraph - 获取依赖图
     * @returns {DependencyGraph} 依赖关系图
     */
    getDependencyGraph() {
        const graph = {
            nodes: [],
            edges: []
        };

        for (const [name, definition] of this.services) {
            graph.nodes.push({
                id: name,
                type: definition.singleton ? 'singleton' : 'transient',
                tags: definition.tags,
                metadata: definition.metadata
            });

            for (const dep of definition.dependencies) {
                graph.edges.push({
                    from: name,
                    to: dep
                });
            }
        }

        return graph;
    }

    /**
     * @function getStatistics - 获取容器统计信息
     * @returns {ContainerStatistics} 统计信息
     */
    getStatistics() {
        const totalServices = this.services.size;
        const singletonServices = Array.from(this.services.values())
            .filter(def => def.singleton).length;
        const transientServices = totalServices - singletonServices;
        const instantiatedSingletons = this.singletonInstances.size;

        return {
            totalServices,
            singletonServices,
            transientServices,
            instantiatedSingletons,
            dependencyCount: Array.from(this.services.values())
                .reduce((total, def) => total + def.dependencies.length, 0)
        };
    }
}

/**
 * @class ScopedDIContainer
 * @description 作用域依赖注入容器，支持临时服务注册
 */
class ScopedDIContainer extends DIContainer {
    constructor(parentContainer) {
        super();
        this.parent = parentContainer;
        this.logger.info('ScopedDIContainer', '作用域容器已创建');
    }

    /**
     * @function resolve - 作用域解析服务
     * @param {string} name - 服务名称
     * @returns {any} 服务实例
     */
    resolve(name) {
        // 优先从当前作用域解析
        if (this.has(name)) {
            return super.resolve(name);
        }

        // 回退到父容器
        if (this.parent && this.parent.has(name)) {
            return this.parent.resolve(name);
        }

        throw new Error(`服务未在当前作用域或父容器中注册: ${name}`);
    }

    /**
     * @function dispose - 释放作用域
     */
    dispose() {
        this.clear();
        this.parent = null;
        this.logger.info('ScopedDIContainer', '作用域容器已释放');
    }
}

/**
 * @typedef {Object} ServiceDefinition
 * @property {string} name - 服务名称
 * @property {Function|any} factory - 工厂函数或实例
 * @property {boolean} singleton - 是否为单例
 * @property {string[]} dependencies - 依赖的服务名称数组
 * @property {string[]} tags - 服务标签
 * @property {Object} metadata - 元数据
 * @property {boolean} lazy - 是否懒加载
 */

/**
 * @typedef {Object} ServiceOptions
 * @property {boolean} [singleton=false] - 是否为单例
 * @property {string[]} [dependencies=[]] - 依赖的服务名称数组
 * @property {string[]} [tags=[]] - 服务标签
 * @property {Object} [metadata={}] - 元数据
 * @property {boolean} [lazy=false] - 是否懒加载
 */

/**
 * @typedef {Object} ValidationResult
 * @property {boolean} valid - 是否有效
 * @property {string[]} errors - 错误信息数组
 * @property {string[]} warnings - 警告信息数组
 */

/**
 * @typedef {Object} DependencyGraph
 * @property {Object[]} nodes - 节点数组
 * @property {Object[]} edges - 边数组
 */

/**
 * @typedef {Object} ContainerStatistics
 * @property {number} totalServices - 总服务数
 * @property {number} singletonServices - 单例服务数
 * @property {number} transientServices - 瞬态服务数
 * @property {number} instantiatedSingletons - 已实例化的单例数
 * @property {number} dependencyCount - 总依赖数
 */

// 创建全局容器实例
if (typeof window !== 'undefined') {
    window.DIContainer = DIContainer;
    window.ScopedDIContainer = ScopedDIContainer;
    
    // 创建全局容器实例
    window.diContainer = new DIContainer();
    
    console.log('✅ 依赖注入容器已加载并可用');
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = { DIContainer, ScopedDIContainer };
}