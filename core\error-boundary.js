/**
 * @file core/error-boundary.js - 统一错误处理边界
 * @description 提供全局错误捕获、分类处理和恢复机制的统一错误处理系统
 * <AUTHOR>
 * @date 2024-01-20
 */

// #region 错误分类枚举

/**
 * @enum {string} 错误分类枚举
 */
const ErrorCategory = {
    NETWORK: 'network',           // 网络错误
    VALIDATION: 'validation',     // 数据验证错误
    AUTHENTICATION: 'auth',       // 认证错误
    BUSINESS: 'business',         // 业务逻辑错误
    SYSTEM: 'system',             // 系统级错误
    DEPENDENCY: 'dependency',     // 依赖错误
    USER_INPUT: 'user_input',     // 用户输入错误
    PERFORMANCE: 'performance',   // 性能错误
    UNKNOWN: 'unknown'            // 未知错误
};

/**
 * @enum {string} 错误严重程度枚举
 */
const ErrorSeverity = {
    CRITICAL: 'critical',         // 关键错误，系统无法继续
    HIGH: 'high',                 // 高级错误，影响核心功能
    MEDIUM: 'medium',             // 中级错误，影响部分功能
    LOW: 'low',                   // 低级错误，用户体验影响小
    INFO: 'info'                  // 信息级别，仅作记录
};

// #endregion

// #region 错误处理器核心类

/**
 * @class ErrorHandler - 统一错误处理器
 * @description 提供错误分类、处理策略和恢复机制的核心错误处理类
 */
class ErrorHandler {
    /**
     * @function constructor - 初始化错误处理器
     * @param {Object} options - 配置选项
     * @param {Object} options.logger - 日志记录器实例
     * @param {Object} options.notificationService - 通知服务实例
     * @param {Object} options.appState - 应用状态管理器
     */
    constructor(options = {}) {
        this.logger = options.logger || window.logger || console;
        this.notificationService = options.notificationService;
        this.appState = options.appState;
        
        // 错误统计
        this.errorStats = {
            total: 0,
            byCategory: new Map(),
            bySeverity: new Map(),
            lastOccurrence: null,
            recoveryAttempts: 0,
            successfulRecoveries: 0
        };
        
        // 错误处理策略映射
        this.handlerStrategies = new Map();
        this.recoveryStrategies = new Map();
        
        // 错误抑制机制（防止错误风暴）
        this.errorSuppression = {
            enabled: true,
            maxSameErrors: 5,
            timeWindow: 5000, // 5秒
            suppressedErrors: new Map()
        };
        
        // 初始化默认处理策略
        this._initializeDefaultStrategies();
        
        this.logger.info('ErrorHandler', '统一错误处理器初始化完成');
    }

    // #region 错误处理核心方法

    /**
     * @function handleError - 处理错误的核心方法
     * @param {Error|string} error - 错误对象或错误消息
     * @param {Object} context - 错误上下文信息
     * @param {string} context.source - 错误来源（模块名称）
     * @param {string} context.operation - 出错的操作
     * @param {Object} context.data - 相关数据
     * @param {string} context.userId - 用户ID（可选）
     * @returns {Object} 错误处理结果
     */
    handleError(error, context = {}) {
        try {
            // 标准化错误对象
            const standardizedError = this._standardizeError(error, context);
            
            // 检查错误抑制
            if (this._shouldSuppressError(standardizedError)) {
                return { handled: true, suppressed: true };
            }
            
            // 分类错误
            const category = this._categorizeError(standardizedError);
            const severity = this._determineSeverity(standardizedError, category);
            
            // 更新统计信息
            this._updateErrorStats(category, severity);
            
            // 创建完整的错误信息对象
            const errorInfo = {
                ...standardizedError,
                category,
                severity,
                timestamp: new Date().toISOString(),
                errorId: this._generateErrorId(),
                context: {
                    source: context.source || 'unknown',
                    operation: context.operation || 'unknown',
                    data: context.data || {},
                    userId: context.userId,
                    userAgent: navigator.userAgent,
                    url: window.location.href
                }
            };
            
            // 记录错误
            this._logError(errorInfo);
            
            // 执行错误处理策略
            const handlingResult = this._executeHandlingStrategy(errorInfo);
            
            // 尝试错误恢复
            const recoveryResult = this._attemptRecovery(errorInfo);
            
            // 通知用户（如果需要）
            this._notifyUser(errorInfo, handlingResult);
            
            return {
                handled: true,
                errorId: errorInfo.errorId,
                category,
                severity,
                handlingResult,
                recoveryResult,
                suppressed: false
            };
            
        } catch (handlingError) {
            // 错误处理器本身出错时的降级处理
            this.logger.error('ErrorHandler', '错误处理器内部错误', {
                originalError: error,
                handlingError: handlingError.message
            });
            
            return {
                handled: false,
                error: '错误处理器内部错误',
                fallback: true
            };
        }
    }

    /**
     * @function handleAsyncError - 处理异步错误
     * @param {Promise} promise - 需要处理的Promise
     * @param {Object} context - 错误上下文
     * @returns {Promise} 包装后的Promise
     */
    async handleAsyncError(promise, context = {}) {
        try {
            return await promise;
        } catch (error) {
            const result = this.handleError(error, context);
            
            // 如果恢复成功，返回恢复结果
            if (result.recoveryResult && result.recoveryResult.success) {
                return result.recoveryResult.data;
            }
            
            // 否则重新抛出错误（但已经被处理和记录）
            throw error;
        }
    }

    /**
     * @function wrapFunction - 包装函数以提供错误处理
     * @param {Function} fn - 要包装的函数
     * @param {Object} context - 错误上下文
     * @returns {Function} 包装后的函数
     */
    wrapFunction(fn, context = {}) {
        const self = this;
        
        return function(...args) {
            try {
                const result = fn.apply(this, args);
                
                // 如果返回Promise，包装异步错误处理
                if (result && typeof result.then === 'function') {
                    return self.handleAsyncError(result, context);
                }
                
                return result;
            } catch (error) {
                self.handleError(error, context);
                throw error;
            }
        };
    }

    // #endregion

    // #region 私有方法 - 错误标准化和分类

    /**
     * @function _standardizeError - 标准化错误对象
     * @private
     */
    _standardizeError(error, context) {
        if (typeof error === 'string') {
            return {
                message: error,
                name: 'CustomError',
                stack: new Error(error).stack
            };
        }
        
        if (error instanceof Error) {
            return {
                message: error.message,
                name: error.name,
                stack: error.stack,
                code: error.code,
                status: error.status
            };
        }
        
        // 处理其他类型的错误对象
        return {
            message: error.toString(),
            name: 'UnknownError',
            stack: new Error(error.toString()).stack,
            originalError: error
        };
    }

    /**
     * @function _categorizeError - 错误分类
     * @private
     */
    _categorizeError(error) {
        const message = error.message.toLowerCase();
        const name = error.name.toLowerCase();
        
        // 网络错误
        if (message.includes('network') || message.includes('fetch') || 
            message.includes('timeout') || error.status >= 500) {
            return ErrorCategory.NETWORK;
        }
        
        // 认证错误
        if (message.includes('auth') || message.includes('login') || 
            message.includes('token') || error.status === 401 || error.status === 403) {
            return ErrorCategory.AUTHENTICATION;
        }
        
        // 验证错误
        if (message.includes('validation') || message.includes('invalid') || 
            message.includes('required') || error.status === 400) {
            return ErrorCategory.VALIDATION;
        }
        
        // 依赖错误
        if (message.includes('未加载') || message.includes('not defined') || 
            message.includes('undefined') || name.includes('reference')) {
            return ErrorCategory.DEPENDENCY;
        }
        
        // 业务逻辑错误
        if (message.includes('订单') || message.includes('order') || 
            message.includes('业务') || message.includes('business')) {
            return ErrorCategory.BUSINESS;
        }
        
        // 性能错误
        if (message.includes('memory') || message.includes('performance') || 
            message.includes('timeout') || message.includes('slow')) {
            return ErrorCategory.PERFORMANCE;
        }
        
        // 系统错误
        if (message.includes('system') || message.includes('内部错误') || 
            error.status >= 500) {
            return ErrorCategory.SYSTEM;
        }
        
        return ErrorCategory.UNKNOWN;
    }

    /**
     * @function _determineSeverity - 确定错误严重程度
     * @private
     */
    _determineSeverity(error, category) {
        // 根据错误分类和消息内容确定严重程度
        switch (category) {
            case ErrorCategory.SYSTEM:
            case ErrorCategory.DEPENDENCY:
                return ErrorSeverity.CRITICAL;
                
            case ErrorCategory.AUTHENTICATION:
            case ErrorCategory.NETWORK:
                return ErrorSeverity.HIGH;
                
            case ErrorCategory.BUSINESS:
            case ErrorCategory.VALIDATION:
                return ErrorSeverity.MEDIUM;
                
            case ErrorCategory.USER_INPUT:
            case ErrorCategory.PERFORMANCE:
                return ErrorSeverity.LOW;
                
            default:
                return ErrorSeverity.MEDIUM;
        }
    }

    // #endregion

    // #region 私有方法 - 错误处理策略

    /**
     * @function _initializeDefaultStrategies - 初始化默认处理策略
     * @private
     */
    _initializeDefaultStrategies() {
        // 网络错误处理策略
        this.handlerStrategies.set(ErrorCategory.NETWORK, {
            logLevel: 'error',
            notify: true,
            retry: true,
            maxRetries: 3,
            retryDelay: 1000
        });
        
        // 认证错误处理策略
        this.handlerStrategies.set(ErrorCategory.AUTHENTICATION, {
            logLevel: 'error',
            notify: true,
            retry: false,
            redirectToLogin: true
        });
        
        // 验证错误处理策略
        this.handlerStrategies.set(ErrorCategory.VALIDATION, {
            logLevel: 'warn',
            notify: true,
            retry: false,
            showFieldErrors: true
        });
        
        // 依赖错误处理策略
        this.handlerStrategies.set(ErrorCategory.DEPENDENCY, {
            logLevel: 'error',
            notify: true,
            retry: true,
            maxRetries: 1,
            fallbackMode: true
        });
        
        // 业务错误处理策略
        this.handlerStrategies.set(ErrorCategory.BUSINESS, {
            logLevel: 'error',
            notify: true,
            retry: false,
            rollback: true
        });
        
        // 系统错误处理策略
        this.handlerStrategies.set(ErrorCategory.SYSTEM, {
            logLevel: 'error',
            notify: true,
            retry: false,
            emergencyMode: true
        });
        
        // 恢复策略
        this._initializeRecoveryStrategies();
    }

    /**
     * @function _initializeRecoveryStrategies - 初始化恢复策略
     * @private
     */
    _initializeRecoveryStrategies() {
        // 网络错误恢复：重试机制
        this.recoveryStrategies.set(ErrorCategory.NETWORK, async (errorInfo) => {
            const strategy = this.handlerStrategies.get(ErrorCategory.NETWORK);
            
            if (strategy.retry && strategy.maxRetries > 0) {
                this.logger.info('ErrorHandler', '开始网络错误恢复重试');
                
                for (let i = 0; i < strategy.maxRetries; i++) {
                    await this._delay(strategy.retryDelay * (i + 1));
                    
                    try {
                        // 这里需要根据具体的错误上下文来决定重试逻辑
                        // 暂时返回成功，实际实现需要根据具体业务场景
                        return { success: true, message: '网络连接已恢复', attempt: i + 1 };
                    } catch (retryError) {
                        this.logger.warn('ErrorHandler', `网络恢复重试${i + 1}失败`, retryError);
                    }
                }
            }
            
            return { success: false, message: '网络恢复失败，请检查网络连接' };
        });
        
        // 认证错误恢复：重新登录
        this.recoveryStrategies.set(ErrorCategory.AUTHENTICATION, async (errorInfo) => {
            this.logger.info('ErrorHandler', '开始认证错误恢复');
            
            try {
                // 清除无效的认证信息
                if (window.sessionManager) {
                    await window.sessionManager.clearSession();
                }
                
                // 重定向到登录页面或显示登录对话框
                if (window.app && window.app.interfaceController) {
                    window.app.interfaceController.showLoginModal();
                }
                
                return { success: true, message: '请重新登录' };
            } catch (recoveryError) {
                return { success: false, message: '认证恢复失败：' + recoveryError.message };
            }
        });
        
        // 依赖错误恢复：降级模式
        this.recoveryStrategies.set(ErrorCategory.DEPENDENCY, async (errorInfo) => {
            this.logger.info('ErrorHandler', '开始依赖错误恢复（降级模式）');
            
            try {
                // 启用降级模式
                if (this.appState) {
                    this.appState.set('system.fallbackMode', true);
                }
                
                return { success: true, message: '已启用降级模式，功能可能受限' };
            } catch (recoveryError) {
                return { success: false, message: '降级模式启动失败：' + recoveryError.message };
            }
        });
    }
    
        /**
         * @function createRetryStrategy - 创建重试策略
         * @description 基于错误类型和上下文创建专用重试策略
         * @param {Object} config - 重试配置
         * @returns {Function} 重试策略函数
         * @private
         */
        _createRetryStrategy(config = {}) {
            const defaultConfig = {
                maxAttempts: 3,
                baseDelay: 1000,
                maxDelay: 10000,
                backoffMultiplier: 2,
                jitterPercent: 0.25,
                retryableErrors: ['network', 'timeout', 'server_error']
            };
            
            const retryConfig = { ...defaultConfig, ...config };
            
            return async (operation, context = {}) => {
                let lastError = null;
                const startTime = Date.now();
                
                for (let attempt = 1; attempt <= retryConfig.maxAttempts; attempt++) {
                    try {
                        // 在重试前执行预处理（如数据刷新）
                        if (attempt > 1 && context.beforeRetry) {
                            await context.beforeRetry(lastError, attempt);
                        }
                        
                        const result = await operation(attempt);
                        
                        // 记录成功的重试
                        if (attempt > 1) {
                            this.logger.success('ErrorHandler', `重试第${attempt}次成功`, {
                                operation: context.operation,
                                totalTime: Date.now() - startTime
                            });
                        }
                        
                        return {
                            success: true,
                            data: result,
                            attempts: attempt,
                            totalTime: Date.now() - startTime
                        };
                        
                    } catch (error) {
                        lastError = error;
                        
                        // 检查是否为可重试错误
                        if (!this._isRetryableError(error, retryConfig.retryableErrors)) {
                            this.logger.info('ErrorHandler', '检测到不可重试错误，停止重试', {
                                error: error.message,
                                operation: context.operation
                            });
                            break;
                        }
                        
                        this.logger.warn('ErrorHandler', `重试第${attempt}次失败`, {
                            error: error.message,
                            operation: context.operation,
                            status: error.status || error.response?.status
                        });
                        
                        // 如果不是最后一次尝试，等待后继续
                        if (attempt < retryConfig.maxAttempts) {
                            const delay = this._calculateBackoffDelay(attempt, retryConfig);
                            this.logger.debug('ErrorHandler', `等待${delay}ms后进行下次重试`);
                            await this._delay(delay);
                        }
                    }
                }
                
                // 所有重试失败
                return {
                    success: false,
                    error: lastError,
                    attempts: retryConfig.maxAttempts,
                    totalTime: Date.now() - startTime
                };
            };
        }
    
        /**
         * @function _isRetryableError - 判断错误是否可重试
         * @private
         */
        _isRetryableError(error, retryableTypes) {
            const errorMessage = error.message.toLowerCase();
            const errorStatus = error.status || error.response?.status;
            
            // 检查HTTP状态码
            if (errorStatus) {
                // 5xx服务器错误通常可重试
                if (errorStatus >= 500) return true;
                
                // 特定的4xx错误可重试（如429限流）
                if (errorStatus === 429) return true;
                
                // 认证错误通常不可重试
                if (errorStatus === 401 || errorStatus === 403) return false;
            }
            
            // 检查错误类型
            return retryableTypes.some(type => {
                switch (type) {
                    case 'network':
                        return errorMessage.includes('network') || 
                               errorMessage.includes('connection') ||
                               errorMessage.includes('fetch');
                    case 'timeout':
                        return errorMessage.includes('timeout') ||
                               errorMessage.includes('timed out');
                    case 'server_error':
                        return errorStatus >= 500;
                    default:
                        return errorMessage.includes(type);
                }
            });
        }
    
        /**
         * @function _calculateBackoffDelay - 计算退避延迟
         * @private
         */
        _calculateBackoffDelay(attempt, config) {
            const { baseDelay, maxDelay, backoffMultiplier, jitterPercent } = config;
            
            // 指数退避：baseDelay * multiplier^(attempt-1)
            const delay = Math.min(
                baseDelay * Math.pow(backoffMultiplier, attempt - 1), 
                maxDelay
            );
            
            // 添加随机抖动
            const jitter = delay * jitterPercent * (Math.random() - 0.5);
            
            return Math.round(delay + jitter);
        }
    
        /**
         * @function createRetryWrapper - 创建重试包装器
         * @description 为操作创建重试包装器，支持自定义重试配置
         * @param {Function} operation - 要包装的操作
         * @param {Object} retryConfig - 重试配置
         * @param {Object} context - 操作上下文
         * @returns {Function} 包装后的操作
         */
        createRetryWrapper(operation, retryConfig = {}, context = {}) {
            const retryStrategy = this._createRetryStrategy(retryConfig);
            
            return async (...args) => {
                const wrappedOperation = async (attempt) => {
                    try {
                        return await operation.apply(this, args);
                    } catch (error) {
                        // 使用统一错误处理器记录错误
                        this.handleError(error, {
                            ...context,
                            data: {
                                ...context.data,
                                attempt,
                                args: args.map(arg => typeof arg === 'object' ? '[Object]' : arg)
                            }
                        });
                        throw error;
                    }
                };
                
                const result = await retryStrategy(wrappedOperation, context);
                
                if (!result.success) {
                    throw result.error;
                }
                
                return result.data;
            };
        }
    /**
     * @function _executeHandlingStrategy - 执行错误处理策略
     * @private
     */
    _executeHandlingStrategy(errorInfo) {
        const strategy = this.handlerStrategies.get(errorInfo.category) || 
                        this.handlerStrategies.get(ErrorCategory.UNKNOWN) ||
                        { logLevel: 'error', notify: false };
        
        const result = {
            logged: false,
            notified: false,
            retried: false,
            strategy: strategy
        };
        
        // 记录日志
        if (strategy.logLevel) {
            this._logError(errorInfo, strategy.logLevel);
            result.logged = true;
        }
        
        return result;
    }

    /**
     * @function _attemptRecovery - 尝试错误恢复
     * @private
     */
    async _attemptRecovery(errorInfo) {
        const recoveryStrategy = this.recoveryStrategies.get(errorInfo.category);
        
        if (!recoveryStrategy) {
            return { attempted: false, reason: '无可用恢复策略' };
        }
        
        try {
            this.errorStats.recoveryAttempts++;
            const result = await recoveryStrategy(errorInfo);
            
            if (result.success) {
                this.errorStats.successfulRecoveries++;
            }
            
            return {
                attempted: true,
                success: result.success,
                message: result.message,
                data: result.data
            };
        } catch (recoveryError) {
            this.logger.error('ErrorHandler', '错误恢复过程中出现异常', {
                originalError: errorInfo,
                recoveryError: recoveryError.message
            });
            
            return {
                attempted: true,
                success: false,
                message: '恢复过程异常：' + recoveryError.message
            };
        }
    }

    // #endregion

    // #region 私有方法 - 辅助功能

    /**
     * @function _shouldSuppressError - 检查是否应该抑制错误（防止错误风暴）
     * @private
     */
    _shouldSuppressError(error) {
        if (!this.errorSuppression.enabled) {
            return false;
        }
        
        const errorKey = `${error.name}:${error.message}`;
        const now = Date.now();
        
        if (!this.errorSuppression.suppressedErrors.has(errorKey)) {
            this.errorSuppression.suppressedErrors.set(errorKey, {
                count: 1,
                firstOccurrence: now,
                lastOccurrence: now
            });
            return false;
        }
        
        const errorData = this.errorSuppression.suppressedErrors.get(errorKey);
        
        // 检查时间窗口
        if (now - errorData.firstOccurrence > this.errorSuppression.timeWindow) {
            // 重置计数器
            errorData.count = 1;
            errorData.firstOccurrence = now;
            errorData.lastOccurrence = now;
            return false;
        }
        
        errorData.count++;
        errorData.lastOccurrence = now;
        
        // 检查是否超过最大错误数
        return errorData.count > this.errorSuppression.maxSameErrors;
    }

    /**
     * @function _updateErrorStats - 更新错误统计信息
     * @private
     */
    _updateErrorStats(category, severity) {
        this.errorStats.total++;
        this.errorStats.lastOccurrence = new Date();
        
        // 按分类统计
        if (!this.errorStats.byCategory.has(category)) {
            this.errorStats.byCategory.set(category, 0);
        }
        this.errorStats.byCategory.set(category, 
            this.errorStats.byCategory.get(category) + 1);
        
        // 按严重程度统计
        if (!this.errorStats.bySeverity.has(severity)) {
            this.errorStats.bySeverity.set(severity, 0);
        }
        this.errorStats.bySeverity.set(severity, 
            this.errorStats.bySeverity.get(severity) + 1);
    }

    /**
     * @function _logError - 记录错误日志
     * @private
     */
    _logError(errorInfo, logLevel = 'error') {
        const logMessage = `${errorInfo.category}错误：${errorInfo.message}`;
        const logData = {
            errorId: errorInfo.errorId,
            category: errorInfo.category,
            severity: errorInfo.severity,
            context: errorInfo.context,
            stack: errorInfo.stack
        };
        
        switch (logLevel) {
            case 'critical':
            case 'error':
                this.logger.error('ErrorHandler', logMessage, logData);
                break;
            case 'warn':
                this.logger.warn('ErrorHandler', logMessage, logData);
                break;
            case 'info':
                this.logger.info('ErrorHandler', logMessage, logData);
                break;
            default:
                this.logger.error('ErrorHandler', logMessage, logData);
        }
    }

    /**
     * @function _notifyUser - 通知用户错误信息
     * @private
     */
    _notifyUser(errorInfo, handlingResult) {
        if (!handlingResult.strategy.notify) {
            return;
        }
        
        // 根据严重程度决定通知方式
        let notificationLevel = 'error';
        let userMessage = this._generateUserFriendlyMessage(errorInfo);
        
        switch (errorInfo.severity) {
            case ErrorSeverity.CRITICAL:
                notificationLevel = 'error';
                break;
            case ErrorSeverity.HIGH:
                notificationLevel = 'error';
                break;
            case ErrorSeverity.MEDIUM:
                notificationLevel = 'warning';
                break;
            case ErrorSeverity.LOW:
                notificationLevel = 'info';
                break;
        }
        
        // 使用通知服务或接口控制器显示错误
        if (this.notificationService) {
            this.notificationService[notificationLevel]('系统提示', userMessage);
        } else if (window.app && window.app.interfaceController) {
            window.app.interfaceController.showError(userMessage);
        }
    }

    /**
     * @function _generateUserFriendlyMessage - 生成用户友好的错误消息
     * @private
     */
    _generateUserFriendlyMessage(errorInfo) {
        const messageMappings = {
            [ErrorCategory.NETWORK]: '网络连接出现问题，请检查网络设置后重试',
            [ErrorCategory.AUTHENTICATION]: '登录状态已过期，请重新登录',
            [ErrorCategory.VALIDATION]: '输入的信息格式不正确，请检查后重新提交',
            [ErrorCategory.BUSINESS]: '操作执行失败，请稍后重试或联系技术支持',
            [ErrorCategory.SYSTEM]: '系统暂时不可用，我们正在努力修复',
            [ErrorCategory.DEPENDENCY]: '系统组件加载失败，请刷新页面重试',
            [ErrorCategory.USER_INPUT]: '请检查输入信息的格式和完整性',
            [ErrorCategory.PERFORMANCE]: '系统响应较慢，请稍候片刻',
            [ErrorCategory.UNKNOWN]: '发生未知错误，请稍后重试'
        };
        
        return messageMappings[errorInfo.category] || messageMappings[ErrorCategory.UNKNOWN];
    }

    /**
     * @function _generateErrorId - 生成唯一错误ID
     * @private
     */
    _generateErrorId() {
        return `ERR_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * @function _delay - 异步延迟函数
     * @private
     */
    _delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // #endregion

    // #region 公共方法 - 配置和状态

    /**
     * @function getErrorStats - 获取错误统计信息
     * @returns {Object} 错误统计数据
     */
    getErrorStats() {
        return {
            ...this.errorStats,
            byCategory: Object.fromEntries(this.errorStats.byCategory),
            bySeverity: Object.fromEntries(this.errorStats.bySeverity),
            recoveryRate: this.errorStats.recoveryAttempts > 0 ? 
                (this.errorStats.successfulRecoveries / this.errorStats.recoveryAttempts) : 0
        };
    }

    /**
     * @function resetStats - 重置错误统计
     */
    resetStats() {
        this.errorStats = {
            total: 0,
            byCategory: new Map(),
            bySeverity: new Map(),
            lastOccurrence: null,
            recoveryAttempts: 0,
            successfulRecoveries: 0
        };
        
        this.errorSuppression.suppressedErrors.clear();
        this.logger.info('ErrorHandler', '错误统计已重置');
    }

    /**
     * @function addCustomStrategy - 添加自定义错误处理策略
     * @param {string} category - 错误分类
     * @param {Object} strategy - 处理策略
     * @param {Function} recoveryFunction - 恢复函数（可选）
     */
    addCustomStrategy(category, strategy, recoveryFunction = null) {
        this.handlerStrategies.set(category, strategy);
        
        if (recoveryFunction) {
            this.recoveryStrategies.set(category, recoveryFunction);
        }
        
        this.logger.info('ErrorHandler', `已添加自定义策略：${category}`);
    }

    // #endregion
}

// #endregion

// #region 错误边界类

/**
 * @class ErrorBoundary - 错误边界管理器
 * @description 管理全局错误捕获和错误边界设置
 */
class ErrorBoundary {
    /**
     * @function constructor - 初始化错误边界
     * @param {ErrorHandler} errorHandler - 错误处理器实例
     */
    constructor(errorHandler) {
        this.errorHandler = errorHandler;
        this.isActive = false;
        this.boundHandlers = new Map();
        
        // 绑定全局错误处理
        this.setupGlobalErrorHandling();
    }

    /**
     * @function setupGlobalErrorHandling - 设置全局错误处理
     */
    setupGlobalErrorHandling() {
        // JavaScript 运行时错误
        window.addEventListener('error', (event) => {
            this.errorHandler.handleError(event.error || event.message, {
                source: 'global',
                operation: 'runtime_error',
                data: {
                    filename: event.filename,
                    lineno: event.lineno,
                    colno: event.colno,
                    type: 'javascript_error'
                }
            });
        });

        // Promise 未捕获的拒绝
        window.addEventListener('unhandledrejection', (event) => {
            this.errorHandler.handleError(event.reason, {
                source: 'global',
                operation: 'unhandled_promise_rejection',
                data: {
                    type: 'promise_rejection',
                    promise: event.promise
                }
            });
            
            // 阻止默认的 unhandled rejection 处理
            event.preventDefault();
        });

        // 资源加载错误
        window.addEventListener('error', (event) => {
            if (event.target !== window) {
                this.errorHandler.handleError(`资源加载失败: ${event.target.src || event.target.href}`, {
                    source: 'global',
                    operation: 'resource_loading',
                    data: {
                        type: 'resource_error',
                        element: event.target.tagName,
                        url: event.target.src || event.target.href
                    }
                });
            }
        }, true);

        this.isActive = true;
        this.errorHandler.logger.info('ErrorBoundary', '全局错误边界已激活');
    }

    /**
     * @function wrapComponent - 包装组件以提供错误边界
     * @param {Function} component - 组件函数
     * @param {string} componentName - 组件名称
     * @returns {Function} 包装后的组件
     */
    wrapComponent(component, componentName) {
        const self = this;
        
        return function wrappedComponent(...args) {
            try {
                return component.apply(this, args);
            } catch (error) {
                self.errorHandler.handleError(error, {
                    source: 'component',
                    operation: componentName,
                    data: {
                        type: 'component_error',
                        args: args
                    }
                });
                
                // 返回错误状态的组件或空组件
                return self._renderErrorFallback(componentName, error);
            }
        };
    }

    /**
     * @function createRetryBoundary - 创建带重试功能的错误边界
     * @param {Function} operation - 要执行的操作
     * @param {Object} options - 重试选项
     * @returns {Function} 包装后的操作
     */
    createRetryBoundary(operation, options = {}) {
        const maxRetries = options.maxRetries || 3;
        const retryDelay = options.retryDelay || 1000;
        const context = options.context || {};
        
        return async (...args) => {
            let lastError;
            
            for (let attempt = 0; attempt <= maxRetries; attempt++) {
                try {
                    return await operation.apply(this, args);
                } catch (error) {
                    lastError = error;
                    
                    this.errorHandler.handleError(error, {
                        ...context,
                        operation: operation.name || 'retry_operation',
                        data: {
                            attempt: attempt + 1,
                            maxRetries: maxRetries + 1,
                            ...context.data
                        }
                    });
                    
                    // 如果不是最后一次尝试，则等待后重试
                    if (attempt < maxRetries) {
                        await new Promise(resolve => setTimeout(resolve, retryDelay * (attempt + 1)));
                    }
                }
            }
            
            // 所有重试都失败，抛出最后的错误
            throw lastError;
        };
    }

    /**
     * @function _renderErrorFallback - 渲染错误回退内容
     * @private
     */
    _renderErrorFallback(componentName, error) {
        this.errorHandler.logger.warn('ErrorBoundary', `组件 ${componentName} 渲染失败，显示回退内容`);
        
        // 返回简单的错误提示或空内容
        return {
            error: true,
            componentName,
            message: '组件暂时不可用',
            originalError: error.message
        };
    }

    /**
     * @function disable - 禁用错误边界
     */
    disable() {
        // 注意：实际上无法完全移除已添加的事件监听器
        // 这里只是标记为非活动状态
        this.isActive = false;
        this.errorHandler.logger.info('ErrorBoundary', '错误边界已禁用');
    }
}

// #endregion

// #region 全局导出和初始化

// 导出类和枚举
if (typeof window !== 'undefined') {
    window.ErrorHandler = ErrorHandler;
    window.ErrorBoundary = ErrorBoundary;
    window.ErrorCategory = ErrorCategory;
    window.ErrorSeverity = ErrorSeverity;
}

// 模块导出（如果支持）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        ErrorHandler,
        ErrorBoundary,
        ErrorCategory,
        ErrorSeverity
    };
}

// #endregion

// #region 开发辅助工具

/**
 * @function createGlobalErrorHandler - 创建全局错误处理器实例
 * @description 便捷函数，用于在应用启动时创建和配置全局错误处理器
 */
function createGlobalErrorHandler() {
    const errorHandler = new ErrorHandler({
        logger: window.logger,
        appState: window.appState,
        notificationService: window.notificationService
    });
    
    const errorBoundary = new ErrorBoundary(errorHandler);
    
    // 设置全局访问
    window.globalErrorHandler = errorHandler;
    window.globalErrorBoundary = errorBoundary;
    
    // 提供便捷方法
    window.handleError = (error, context) => errorHandler.handleError(error, context);
    window.wrapAsync = (promise, context) => errorHandler.handleAsyncError(promise, context);
    window.wrapFunction = (fn, context) => errorHandler.wrapFunction(fn, context);
    
    console.log('🛡️ 全局错误处理系统已初始化');
    
    return { errorHandler, errorBoundary };
}

/**
 * @function migrateExistingErrorHandling - 迁移现有错误处理到统一系统
 * @description 扫描并更新项目中现有的错误处理代码
 */
function migrateExistingErrorHandling() {
    if (!window.globalErrorHandler) {
        console.warn('⚠️ 全局错误处理器未初始化，无法执行迁移');
        return;
    }
    
    console.group('🔄 开始迁移现有错误处理');
    
    let migratedCount = 0;
    const migrationReport = {
        services: [],
        components: [],
        functions: []
    };
    
    // 1. 迁移核心服务的错误处理
    const servicesToMigrate = [
        'ApiService', 'LLMService', 'OrderParser', 'ImageService',
        'SmartSelectionService', 'SessionManager', 'ResilienceManager'
    ];
    
    servicesToMigrate.forEach(serviceName => {
        const serviceKey = serviceName.toLowerCase();
        const service = window[serviceKey] || window[serviceName];
        
        if (service && typeof service === 'object') {
            // 包装服务的方法
            Object.getOwnPropertyNames(Object.getPrototypeOf(service))
                .filter(prop => typeof service[prop] === 'function' && prop !== 'constructor')
                .forEach(methodName => {
                    const originalMethod = service[methodName];
                    service[methodName] = window.globalErrorHandler.wrapFunction(originalMethod, {
                        source: serviceName,
                        operation: methodName
                    });
                    migratedCount++;
                });
            
            migrationReport.services.push({
                name: serviceName,
                found: true,
                methodsWrapped: Object.getOwnPropertyNames(Object.getPrototypeOf(service)).length - 1
            });
        } else {
            migrationReport.services.push({
                name: serviceName,
                found: false,
                reason: '服务实例未找到'
            });
        }
    });
    
    // 2. 迁移全局函数
    const globalFunctionsToWrap = [
        'loadSystemData', 'performCompleteAutoSelection', 'executeMemoryOptimization'
    ];
    
    globalFunctionsToWrap.forEach(funcName => {
        if (typeof window[funcName] === 'function') {
            const originalFunc = window[funcName];
            window[funcName] = window.globalErrorHandler.wrapFunction(originalFunc, {
                source: 'global',
                operation: funcName
            });
            migratedCount++;
            
            migrationReport.functions.push({
                name: funcName,
                migrated: true
            });
        } else {
            migrationReport.functions.push({
                name: funcName,
                migrated: false,
                reason: '函数未找到'
            });
        }
    });
    
    // 3. 输出迁移报告
    console.log('📊 迁移统计:');
    console.log(`- 已迁移项目: ${migratedCount}个`);
    console.log(`- 服务迁移: ${migrationReport.services.filter(s => s.found).length}/${migrationReport.services.length}`);
    console.log(`- 函数迁移: ${migrationReport.functions.filter(f => f.migrated).length}/${migrationReport.functions.length}`);
    
    console.table(migrationReport.services);
    console.table(migrationReport.functions);
    
    console.groupEnd();
    
    // 存储迁移状态
    window.errorHandlingMigrationComplete = true;
    window.errorHandlingMigrationReport = migrationReport;
    
    return migrationReport;
}

/**
 * @function validateErrorHandlingIntegration - 验证错误处理集成状态
 * @description 检查统一错误处理系统的集成状态和健康度
 */
function validateErrorHandlingIntegration() {
    console.group('🔍 验证错误处理集成状态');
    
    const validationResults = {
        coreComponents: {},
        globalHandlers: {},
        diIntegration: {},
        coverage: {},
        health: {}
    };
    
    // 1. 检查核心组件
    validationResults.coreComponents.errorHandler = !!window.globalErrorHandler;
    validationResults.coreComponents.errorBoundary = !!window.globalErrorBoundary;
    validationResults.coreComponents.helperFunctions = !!(window.handleError && window.wrapAsync && window.wrapFunction);
    
    // 2. 检查全局处理器
    validationResults.globalHandlers.windowError = true; // 总是有
    validationResults.globalHandlers.unhandledRejection = true; // 总是有
    validationResults.globalHandlers.resourceError = true; // 总是有
    
    // 3. 检查DI集成
    if (window.diContainer) {
        try {
            validationResults.diIntegration.registered = !!(
                window.diContainer.resolve('errorHandler') && 
                window.diContainer.resolve('errorBoundary')
            );
        } catch (e) {
            validationResults.diIntegration.registered = false;
        }
    } else {
        validationResults.diIntegration.registered = false;
        validationResults.diIntegration.reason = 'DI容器未初始化';
    }
    
    // 4. 检查覆盖度
    const expectedServices = ['ApiService', 'LLMService', 'OrderParser', 'SmartSelectionService'];
    const wrappedServices = expectedServices.filter(service => {
        const instance = window[service.toLowerCase()] || window[service];
        return instance && instance.constructor && instance.constructor.name.includes('Wrapped');
    });
    
    validationResults.coverage.servicesCovered = `${wrappedServices.length}/${expectedServices.length}`;
    validationResults.coverage.migrationComplete = window.errorHandlingMigrationComplete || false;
    
    // 5. 健康度检查
    if (window.globalErrorHandler) {
        const stats = window.globalErrorHandler.getErrorStats();
        validationResults.health.totalErrors = stats.total;
        validationResults.health.recoveryRate = stats.recoveryRate;
        validationResults.health.lastError = stats.lastOccurrence;
    }
    
    // 计算总体健康度分数
    const healthScore = Object.values(validationResults.coreComponents).filter(Boolean).length * 25 +
                       (validationResults.diIntegration.registered ? 25 : 0);
    
    console.log('📊 集成状态总览:');
    console.table(validationResults);
    console.log(`🎯 健康度评分: ${healthScore}/100`);
    
    if (healthScore >= 75) {
        console.log('✅ 错误处理系统集成良好');
    } else if (healthScore >= 50) {
        console.log('⚠️ 错误处理系统部分集成，建议完善');
    } else {
        console.log('❌ 错误处理系统集成不足，需要修复');
    }
    
    console.groupEnd();
    
    return { validationResults, healthScore };
}

// 导出迁移和验证函数
if (typeof window !== 'undefined') {
    window.migrateExistingErrorHandling = migrateExistingErrorHandling;
    window.validateErrorHandlingIntegration = validateErrorHandlingIntegration;
}
// 自动初始化（如果在浏览器环境且Logger已就绪）
if (typeof window !== 'undefined' && window.logger) {
    // 延迟初始化，等待其他核心模块加载完成
    setTimeout(() => {
        if (!window.globalErrorHandler) {
            createGlobalErrorHandler();
        }
    }, 100);
}

// #endregion