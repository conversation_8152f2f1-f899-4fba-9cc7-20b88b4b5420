# 核心函数和架构概览

## 主要类和核心方法

### 1. OTAOrderApp (core/app.js) - 主应用控制器
**关键方法**:
- `initialize()` - 应用初始化
- `initializeCoreServices()` - 核心服务初始化
- `loadSystemData()` - 系统数据加载
- `handleLogin()` / `handleLogout()` - 用户认证
- `checkLLMConnections()` - AI服务连接检查

### 2. SmartSelectionService (core/smart-selection.js) - 五维智能选择引擎
**核心功能**:
- `performCompleteAutoSelection()` - 执行完整的五维自动选择
- `selectBackendUser()` - 用户选择算法
- `selectSubCategory()` - 服务类型选择
- `selectCarType()` - 车型选择算法
- `selectDrivingRegion()` - 区域选择
- `selectLanguages()` - 语言选择
- `applyProfile()` - OTA Profile应用

### 3. LLMService (services/llm-service.js) - AI服务引擎
**主要方法**:
- `processOrderText()` - 处理订单文本
- `callGemini()` - Gemini API调用
- `checkGeminiConnection()` - 连接状态检查
- `parseResponse()` - AI响应解析
- `extractOrderNumber()` - 订单号提取

### 4. OrderParser (services/order-parser.js) - 订单解析器
**核心功能**:
- `parseOrders()` - 主要订单解析方法
- `parseLLMOrders()` - LLM订单解析
- `detectOtaType()` - OTA类型检测
- `validateOrder()` - 订单验证

### 5. ApiService (services/api-service.js) - API服务层
**关键方法**:
- `login()` / `logout()` - 认证管理
- `createOrder()` - 订单创建
- `getBackendUsers()` - 获取用户列表
- `getSubCategories()` - 获取服务类型
- `getCarTypes()` - 获取车型数据
- `getDrivingRegions()` - 获取区域数据
- `getLanguages()` - 获取语言数据

## 数据流架构

```
用户输入 → OrderParser → LLMService → SmartSelectionService → ApiService → 订单创建
    ↓           ↓            ↓               ↓                ↓
  文本/图片   OTA检测   AI解析处理   五维智能选择   GoMyHire API   最终订单
```

## 系统集成点

### AI服务集成
- **DeepSeek API**: 主要LLM服务 (api.deepseek.com)
- **Gemini API**: 备用LLM服务 (generativelanguage.googleapis.com)
- **Google Vision**: 图片OCR识别

### 外部API集成  
- **GoMyHire API**: 订单管理系统 (gomyhire.com.my/api)
- **Google Maps API**: 地址搜索和补全

### 核心数据结构
- **订单数据**: 标准化的订单对象结构
- **选择结果**: 五维选择的结果集合
- **Profile配置**: OTA模板配置数据
- **系统缓存**: 用户、车型、区域、语言数据缓存

## 性能优化特性
- **模块化加载**: 按需加载非关键模块
- **智能缓存**: LLM响应和系统数据缓存
- **故障切换**: AI服务自动故障转移
- **内存管理**: 定期内存清理和优化