# 推荐的开发命令和工具

## Windows系统常用命令

### 文件操作
```bash
# 查看目录内容 (Windows)
dir                    # 列出当前目录文件
dir /s                 # 递归列出子目录
tree /f                # 显示目录树结构

# 文件搜索
findstr "pattern" *.js # 在JS文件中搜索模式
findstr /s "TODO" *    # 递归搜索TODO注释

# 文件复制和移动
copy source.js dest.js # 复制文件
move old.js new.js     # 重命名/移动文件
```

### Git版本控制
```bash
# 基础Git操作
git status             # 查看状态
git add .              # 添加所有更改
git commit -m "描述"   # 提交更改
git push origin main   # 推送到远程

# 分支操作
git branch             # 查看分支
git checkout -b feature/new-feature # 创建新分支
git merge feature-branch # 合并分支

# 查看历史
git log --oneline      # 简洁日志
git diff               # 查看差异
```

### 开发调试
```bash
# 启动本地服务器 (如果需要HTTP协议)
python -m http.server 8000  # Python服务器
npx http-server             # Node.js服务器

# 网络测试
ping api.deepseek.com       # 测试API连通性
curl -I https://gomyhire.com.my/api # 测试API响应
```

## 浏览器开发工具

### Chrome DevTools
- **Console**: 查看应用日志和错误信息
- **Network**: 监控API调用和性能
- **Application**: 查看LocalStorage和缓存
- **Performance**: 分析页面性能
- **Sources**: 调试JavaScript代码

### 推荐插件
- **JSON Formatter**: 格式化API响应
- **EditThisCookie**: 管理Cookie和会话
- **Lighthouse**: 性能和SEO分析

## 项目特定命令

### 直接运行
```bash
# 直接在浏览器打开
start index.html       # Windows
open index.html         # macOS
xdg-open index.html     # Linux
```

### 文件监控 (可选)
```bash
# 使用Live Server插件 (VS Code)
# 或者使用简单HTTP服务器
npx live-server --port=8080
```

### API测试
```bash
# 测试DeepSeek API
curl -X POST "https://api.deepseek.com/chat/completions" \
  -H "Authorization: Bearer your-key" \
  -H "Content-Type: application/json" \
  -d '{"model":"deepseek-chat","messages":[{"role":"user","content":"test"}]}'

# 测试GoMyHire API
curl -X GET "https://gomyhire.com.my/api/backend_users" \
  -H "Authorization: Bearer your-token"
```

## 代码质量工具

### 手动代码检查
- **语法检查**: 浏览器Console查看语法错误
- **网络监控**: DevTools Network标签页监控API调用
- **性能分析**: 使用Performance标签页分析加载时间
- **内存监控**: Memory标签页检查内存泄漏

### 推荐在线工具
- **JSONLint**: 验证JSON格式
- **JS Hint**: JavaScript代码质量检查
- **Can I Use**: 浏览器兼容性查询

## 故障排除命令
```bash
# 清除浏览器缓存 (硬刷新)
Ctrl + F5              # Windows
Cmd + Shift + R        # macOS

# 查看详细错误信息
# 在浏览器Console中执行
localStorage.clear()   # 清除本地存储
sessionStorage.clear() # 清除会话存储

# 网络连接测试
nslookup api.deepseek.com     # DNS解析测试
tracert gomyhire.com.my       # 路由追踪
```

## 性能优化命令
```bash
# 压缩JavaScript (可选)
npx terser app.js -o app.min.js

# 优化图片
npx imagemin *.png --out-dir=optimized

# 分析包大小
npx bundlephobia analyze package.json
```

**注意**: 本项目是纯前端应用，无需Node.js构建过程，大部分开发可以直接在浏览器中进行。