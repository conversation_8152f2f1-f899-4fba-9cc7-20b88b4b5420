# 代码风格和约定

## JavaScript编码风格

### 命名约定
- **类名**: PascalCase (如 `OTAOrderApp`, `SmartSelectionService`)
- **方法/函数**: camelCase (如 `initializeCore`, `handleLogin`)
- **变量**: camelCase (如 `userInfo`, `orderData`)
- **常量**: UPPER_SNAKE_CASE (如 `SYSTEM_CONFIG`, `LOG_LEVELS`)
- **文件名**: kebab-case (如 `order-parser.js`, `smart-selection.js`)

### 代码组织
- **模块导出**: 使用全局window对象导出 (如 `window.OrderParser = OrderParser`)
- **依赖声明**: 文件顶部明确声明依赖关系
- **版本标识**: 每个模块包含版本号 (如 `const version = 'v2.0.1'`)
- **错误处理**: 统一使用try-catch和logger.error记录

### 注释规范
- **文件头**: 包含模块描述、作者、版本信息
- **函数注释**: 使用JSDoc格式，包含@param和@returns
- **代码段**: 使用#region和#endregion标记逻辑块
- **复杂逻辑**: 必须有中文注释说明

### 示例代码风格
```javascript
/**
 * @file 订单解析引擎
 * @description 智能解析OTA订单内容，支持文本和图片识别
 * @version v2.0.1
 */

class OrderParser {
    /**
     * @function parseOrder - 解析订单内容
     * @param {string} text - 订单文本内容
     * @param {string} otaType - OTA类型
     * @returns {Object} 解析结果
     */
    async parseOrder(text, otaType = 'auto') {
        try {
            // #region 订单类型检测
            const detectedType = this.detectOTAType(text);
            // #endregion

            return this.processOrder(text, detectedType);
        } catch (error) {
            logger.error('OrderParser', '订单解析失败', { error, text });
            throw error;
        }
    }
}

// 全局导出
window.OrderParser = OrderParser;
```

## CSS样式约定
- **BEM命名**: 使用BEM (Block__Element--Modifier) 命名法
- **响应式**: 移动优先设计，使用媒体查询
- **变量**: 使用CSS自定义属性定义主题色彩
- **组件化**: 每个功能模块独立的CSS文件

## 项目结构约定
- **单一职责**: 每个文件专注一个功能模块
- **依赖层次**: 明确的依赖关系，避免循环依赖
- **配置集中**: 所有配置统一在config.js中管理
- **错误统一**: 使用logger系统统一记录和处理错误