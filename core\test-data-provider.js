/**
 * @file test-data-provider.js - 测试数据提供器
 * @description 为开发和测试环境提供模拟数据，支持API数据降级
 * <AUTHOR> IDE
 * @created_at 2025-01-08
 * @version v1.0.0
 */

/**
 * @class TestDataProvider - 测试数据提供器
 * @description 提供测试数据和API降级功能
 */
class TestDataProvider {
    /**
     * @function constructor - 构造函数
     * @description 初始化测试数据提供器
     */
    constructor() {
        this.isTestMode = false;
        this.testData = this.initializeTestData();
        
        logger.info('测试数据', '测试数据提供器初始化完成');
    }

    /**
     * @function initializeTestData - 初始化测试数据
     * @description 创建用于测试的模拟数据
     * @returns {object} 测试数据对象
     */
    initializeTestData() {
        return {
            // 测试用后台用户
            backendUsers: [
                { id: 1, name: 'Test Admin', email: '<EMAIL>', phone: '', role_id: 'admin' },
                { id: 37, name: 'Test SMW', email: '<EMAIL>', phone: '', role_id: 'operator' },
                { id: 310, name: 'Test Jcy', email: '<EMAIL>', phone: '', role_id: 'operator' }
            ],

            // 测试用子分类
            subCategories: [
                { id: 1, main_category: 'Airport Transfer', name: 'Pickup', preset_data: {}, required_fields: [] },
                { id: 2, main_category: 'Airport Transfer', name: 'Drop-off', preset_data: {}, required_fields: [] },
                { id: 3, main_category: 'Charter', name: 'Half Day', preset_data: {}, required_fields: [] }
            ],

            // 测试用车型
            carTypes: [
                { id: 1, type: 'Comfort 5 Seater', seat_number: 5, priority: 1 },
                { id: 2, type: 'Comfort 7 Seater', seat_number: 7, priority: 2 },
                { id: 3, type: 'Premium 5 Seater', seat_number: 5, priority: 3 }
            ],

            // 测试用驾驶区域
            drivingRegions: [
                { id: 1, name: 'Kuala Lumpur', code: 'KL' },
                { id: 2, name: 'Selangor', code: 'SEL' },
                { id: 3, name: 'Penang', code: 'PG' }
            ],

            // 测试用语言
            languages: [
                { id: 2, name: 'English (EN)', code: 'EN' },
                { id: 3, name: 'Malay (MY)', code: 'MY' },
                { id: 4, name: 'Chinese (CN)', code: 'CN' }
            ]
        };
    }

    /**
     * @function checkAndEnableTestModeIfNeeded - 检查并启用测试模式
     * @description 根据条件判断是否需要启用测试模式
     * @param {object} appState - 应用状态对象
     * @returns {boolean} 是否启用了测试模式
     */
    checkAndEnableTestModeIfNeeded(appState) {
        // 检查是否需要启用测试模式的条件
        const needsTestMode = (
            // 如果API数据为空或不完整
            !appState.backendUsers || appState.backendUsers.length === 0 ||
            !appState.carTypes || appState.carTypes.length === 0 ||
            // 或者在开发环境中
            window.location.hostname === 'localhost' ||
            window.location.hostname === '127.0.0.1' ||
            // 或者URL参数中指定了测试模式
            new URLSearchParams(window.location.search).get('testMode') === 'true'
        );

        if (needsTestMode) {
            this.enableTestMode();
            logger.info('测试数据', '自动启用测试模式');
            return true;
        }

        return false;
    }

    /**
     * @function enableTestMode - 启用测试模式
     * @description 启用测试数据模式
     */
    enableTestMode() {
        this.isTestMode = true;
        logger.info('测试数据', '测试模式已启用');
    }

    /**
     * @function disableTestMode - 禁用测试模式
     * @description 禁用测试数据模式
     */
    disableTestMode() {
        this.isTestMode = false;
        logger.info('测试数据', '测试模式已禁用');
    }

    /**
     * @function populateAppStateWithTestData - 用测试数据填充应用状态
     * @description 将测试数据填充到应用状态中
     * @param {object} appState - 应用状态对象
     */
    populateAppStateWithTestData(appState) {
        if (!this.isTestMode) {
            logger.warn('测试数据', '测试模式未启用，跳过数据填充');
            return;
        }

        try {
            // 填充测试数据到应用状态
            appState.backendUsers = [...this.testData.backendUsers];
            appState.subCategories = [...this.testData.subCategories];
            appState.carTypes = [...this.testData.carTypes];
            appState.drivingRegions = [...this.testData.drivingRegions];
            appState.languages = [...this.testData.languages];

            // 缓存测试数据
            appState.cacheSystemData('backendUsers', this.testData.backendUsers);
            appState.cacheSystemData('subCategories', this.testData.subCategories);
            appState.cacheSystemData('carTypes', this.testData.carTypes);
            appState.cacheSystemData('drivingRegions', this.testData.drivingRegions);
            appState.cacheSystemData('languages', this.testData.languages);

            logger.success('测试数据', '测试数据填充完成', {
                backendUsers: this.testData.backendUsers.length,
                subCategories: this.testData.subCategories.length,
                carTypes: this.testData.carTypes.length,
                drivingRegions: this.testData.drivingRegions.length,
                languages: this.testData.languages.length
            });

        } catch (error) {
            logger.error('测试数据', '测试数据填充失败', error);
        }
    }

    /**
     * @function getTestOrderData - 获取测试订单数据
     * @description 生成用于测试的订单数据
     * @returns {object} 测试订单数据
     */
    getTestOrderData() {
        return {
            pickup_location: 'KLIA Terminal 1',
            destination: 'Kuala Lumpur City Center',
            pickup_date: '15-01-2025',
            pickup_time: '14:30',
            passenger_number: 2,
            customer_name: 'Test Customer',
            customer_contact: '+60123456789',
            ota_reference_number: 'TEST-' + Date.now(),
            ota: 'test-platform',
            ota_price: 150.00,
            luggage_number: 2,
            driving_region_id: 1,
            car_type_id: 1,
            sub_category_id: 1,
            incharge_by_backend_user_id: 1,
            languages_id_array: {"0": "2", "1": "4"},
            special_request: 'Test order for development'
        };
    }

    /**
     * @function getDataSummary - 获取测试数据摘要
     * @description 获取测试数据的统计摘要
     * @returns {object} 数据摘要
     */
    getDataSummary() {
        return {
            isTestMode: this.isTestMode,
            backendUsers: this.testData.backendUsers.length,
            subCategories: this.testData.subCategories.length,
            carTypes: this.testData.carTypes.length,
            drivingRegions: this.testData.drivingRegions.length,
            languages: this.testData.languages.length,
            totalRecords: this.testData.backendUsers.length + 
                         this.testData.subCategories.length + 
                         this.testData.carTypes.length + 
                         this.testData.drivingRegions.length + 
                         this.testData.languages.length
        };
    }
}

// 创建全局实例
window.testDataProvider = new TestDataProvider();

logger.info('模块', '测试数据提供器模块加载完成');
