/**
 * @file enhanced-login-handler.js - 增强登录处理器
 * @description 修复登录功能问题，提供可靠的登录体验
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-01-16
 */

/**
 * @class EnhancedLoginHandler - 增强登录处理器
 * @description 提供可靠的登录功能，包含错误处理、状态管理和用户反馈
 */
class EnhancedLoginHandler {
    constructor() {
            this.formData = null;
            this.isProcessing = false;
            
            // 从DI容器获取ErrorHandler（如果可用）
            this.errorHandler = window.resolveService ? window.resolveService('errorHandler') : null;
            
            // 如果有ErrorHandler，使用统一重试机制
            if (this.errorHandler) {
                this.loginRetry = this.errorHandler.createRetryWrapper(
                    this._performLoginOperation.bind(this),
                    {
                        maxAttempts: 3,
                        baseDelay: 2000,
                        maxDelay: 8000,
                        backoffMultiplier: 2,
                        retryableErrors: ['network', 'timeout', 'server_error', '503', '502', '401']
                    },
                    {
                        source: 'EnhancedLoginHandler',
                        operation: 'login',
                        beforeRetry: this._beforeLoginRetry.bind(this)
                    }
                );
                logger.success('EnhancedLoginHandler', '已集成统一重试机制');
            } else {
                // 降级到传统重试机制
                this.retryCount = 0;
                this.maxRetries = 3;
                this.retryDelay = 2000;
                logger.warn('EnhancedLoginHandler', '使用传统重试机制');
            }
        }

    
    /**
     * @function init - 初始化登录处理器
     * @description 设置事件监听器和初始状态
     */
    init() {
        // 确保在DOM加载完成后绑定事件
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.bindEvents());
        } else {
            this.bindEvents();
        }
        
        // 检查本地模式状态
        this.checkLocalModeStatus();
        
        logger.info('EnhancedLoginHandler', '增强登录处理器初始化完成');
    }
    
    /**
     * @function bindEvents - 绑定登录相关事件
     * @description 强化事件绑定，确保登录功能正常工作
     */
    bindEvents() {
        const loginForm = document.getElementById('loginForm');
        const loginModal = document.getElementById('loginModal');
        
        if (loginForm) {
            // 移除可能存在的旧事件监听器
            loginForm.removeEventListener('submit', this.handleLogin.bind(this));
            
            // 添加新的事件监听器
            loginForm.addEventListener('submit', this.handleLogin.bind(this));
            
            // 添加输入框事件监听器
            const emailInput = document.getElementById('email');
            const passwordInput = document.getElementById('password');
            
            if (emailInput) {
                emailInput.addEventListener('input', () => this.clearError());
            }
            
            if (passwordInput) {
                passwordInput.addEventListener('input', () => this.clearError());
                passwordInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        this.handleLogin(e);
                    }
                });
            }
            
            logger.success('EnhancedLoginHandler', '登录事件绑定完成');
        } else {
            logger.warn('EnhancedLoginHandler', '未找到登录表单，将在1秒后重试');
            setTimeout(() => this.bindEvents(), 1000);
        }
    }
    
    /**
     * @function checkLocalModeStatus - 检查本地模式状态
     * @description 检查是否启用本地模式，并相应调整UI
     */
    checkLocalModeStatus() {
        if (window.localDataProvider?.isLocalModeEnabled()) {
            logger.info('EnhancedLoginHandler', '检测到本地模式已启用');
            this.handleLocalModeLogin();
        }
    }
    
    /**
     * @function handleLogin - 处理登录请求
     * @description 统一的登录处理方法，包含完整的错误处理和用户反馈
     * @param {Event} event - 表单提交事件
     */
    async handleLogin(event) {
        event.preventDefault();
        
        // 防止重复提交
        if (this.isProcessing) {
            logger.warn('EnhancedLoginHandler', '登录正在处理中，忽略重复请求');
            return;
        }
        
        this.isProcessing = true;
        
        try {
            // 立即显示加载状态
            this.showLoadingState();
            
            // 获取表单数据
            const formData = this.getFormData();
            
            // 验证表单数据
            const validation = this.validateFormData(formData);
            if (!validation.valid) {
                throw new Error(validation.message);
            }
            
            // 检查本地模式
            if (window.localDataProvider?.isLocalModeEnabled()) {
                await this.handleLocalModeLogin();
                return;
            }
            
            // 执行实际登录
            await this.performLogin(formData);
            
        } catch (error) {
            logger.error('EnhancedLoginHandler', '登录失败', error);
            this.handleLoginError(error);
        } finally {
            this.isProcessing = false;
            this.hideLoadingState();
        }
    }
    
    /**
     * @function getFormData - 获取表单数据
     * @returns {object} 表单数据对象
     */
    getFormData() {
        const email = document.getElementById('email')?.value?.trim() || '';
        const password = document.getElementById('password')?.value || '';
        
        return { email, password };
    }
    
    /**
     * @function validateFormData - 验证表单数据
     * @param {object} formData - 表单数据
     * @returns {object} 验证结果
     */
    validateFormData(formData) {
        if (!formData.email) {
            return { valid: false, message: '请输入邮箱地址' };
        }
        
        if (!formData.password) {
            return { valid: false, message: '请输入密码' };
        }
        
        // 简单的邮箱格式验证
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(formData.email)) {
            return { valid: false, message: '请输入有效的邮箱地址' };
        }
        
        return { valid: true };
    }
    
    /**
     * @function performLogin - 执行实际登录
     * @param {object} formData - 表单数据
     */
    async performLogin(formData) {
                this.formData = formData;
                
                try {
                    this.showLoadingState();
                    
                    let result;
                    
                    if (this.errorHandler && this.loginRetry) {
                        // 使用统一重试机制
                        logger.info('EnhancedLoginHandler', '使用统一重试机制执行登录');
                        result = await this.loginRetry(formData);
                    } else {
                        // 降级到传统重试机制
                        logger.warn('EnhancedLoginHandler', '降级到传统重试机制');
                        result = await this._legacyPerformLogin(formData);
                    }
                    
                    await this.handleLoginSuccess(result);
                    
                } catch (error) {
                    this.handleLoginError(error);
                    
                    // 检查是否需要降级重试
                    if (!this.errorHandler && this.shouldRetry(error)) {
                        await this.retryLogin(formData);
                    } else {
                        throw error;
                    }
                } finally {
                    this.hideLoadingState();
                }
            }

    
        /**
         * @function _performLoginOperation - 内部登录操作（供重试机制使用）
         * @description 实际的登录操作，由ErrorHandler重试机制包装
         * @private
         */
        async _performLoginOperation(formData) {
            if (!window.app || !window.app.apiService) {
                throw new Error('系统未初始化，请刷新页面重试');
            }
    
            // 执行登录
            const result = await window.app.apiService.login(formData.username, formData.password);
            
            if (!result || !result.token) {
                throw new Error('登录失败：无效的响应数据');
            }
            
            return result;
        }
    
        /**
         * @function _beforeLoginRetry - 登录重试前的预处理
         * @description 在每次重试前执行的清理和准备工作
         * @private
         */
        async _beforeLoginRetry(error, attempt) {
            logger.info('EnhancedLoginHandler', `登录重试前预处理 - 第${attempt}次`, {
                error: error.message
            });
            
            // 更新UI显示重试状态
            this.showRetryState(attempt);
            
            // 如果是认证错误，可能需要清除缓存的token
            if (error.message.includes('401') || error.message.includes('认证')) {
                if (window.app && window.app.sessionManager) {
                    window.app.sessionManager.clearSession();
                    logger.debug('EnhancedLoginHandler', '已清除session缓存');
                }
            }
        }    
        
            /**
             * @function _legacyPerformLogin - 传统登录机制（降级使用）
             * @description 当ErrorHandler不可用时的降级登录方法
             * @private
             */
            async _legacyPerformLogin(formData) {
                if (!window.app || !window.app.apiService) {
                    throw new Error('系统未初始化，请刷新页面重试');
                }
        
                const result = await window.app.apiService.login(formData.username, formData.password);
                
                if (!result || !result.token) {
                    throw new Error('登录失败：无效的响应数据');
                }
                
                return result;
            }    /**
     * @function handleLocalModeLogin - 处理本地模式登录
     */
    async handleLocalModeLogin() {
        logger.info('EnhancedLoginHandler', '处理本地模式登录');
        
        try {
            // 模拟登录延迟
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // 填充本地数据
            if (window.localDataProvider && window.app?.appState) {
                window.localDataProvider.populateAppState(window.app.appState);
            }
            
            // 隐藏登录界面
            this.hideLoginModal();
            
            // 显示主应用
            this.showMainApp();
            
            // 显示本地模式通知
            this.showLocalModeNotification();
            
            logger.success('EnhancedLoginHandler', '本地模式登录完成');
            
        } catch (error) {
            logger.error('EnhancedLoginHandler', '本地模式登录失败', error);
            throw new Error('本地模式初始化失败');
        }
    }
    
    /**
     * @function handleLoginSuccess - 处理登录成功
     * @param {object} result - 登录结果
     */
    async handleLoginSuccess(result) {
        logger.success('EnhancedLoginHandler', '登录成功', { user: result.user?.email });
        
        try {
            // 初始化认证功能
            if (window.app?.initializeAuthenticatedFeatures) {
                await window.app.initializeAuthenticatedFeatures();
            }
            
            // 隐藏登录界面
            this.hideLoginModal();
            
            // 显示主应用
            this.showMainApp();
            
            // 显示成功消息
            this.showSuccessMessage('登录成功！');
            
            // 重置重试计数
            this.retryCount = 0;
            
        } catch (error) {
            logger.error('EnhancedLoginHandler', '登录后初始化失败', error);
            throw new Error('登录成功但初始化失败，请刷新页面');
        }
    }
    
    /**
     * @function shouldRetry - 判断是否应该重试
     * @param {Error} error - 错误对象
     * @returns {boolean} 是否应该重试
     */
    shouldRetry(error) {
        if (this.retryCount >= this.maxRetries) {
            return false;
        }
        
        // 网络错误或超时错误可以重试
        const retryableErrors = [
            'Network Error',
            'timeout',
            'ECONNREFUSED',
            'ETIMEDOUT'
        ];
        
        return retryableErrors.some(errorType => 
            error.message.toLowerCase().includes(errorType.toLowerCase())
        );
    }
    
    /**
     * @function retryLogin - 重试登录
     * @param {object} formData - 表单数据
     */
    async retryLogin(formData) {
        this.retryCount++;
        
        logger.info('EnhancedLoginHandler', `登录重试 ${this.retryCount}/${this.maxRetries}`);
        
        // 显示重试状态
        this.showRetryState(this.retryCount);
        
        // 等待重试延迟
        await new Promise(resolve => setTimeout(resolve, this.retryDelay));
        
        // 递归调用登录
        await this.performLogin(formData);
    }
    
    /**
     * @function handleLoginError - 处理登录错误
     * @param {Error} error - 错误对象
     */
    handleLoginError(error) {
        let errorMessage = error.message || '登录失败，请重试';
        
        // 根据错误类型提供更友好的错误信息
        if (error.message.includes('401') || error.message.includes('Unauthorized')) {
            errorMessage = '邮箱或密码错误，请检查后重试';
        } else if (error.message.includes('Network')) {
            errorMessage = '网络连接失败，请检查网络设置';
        } else if (error.message.includes('timeout')) {
            errorMessage = '请求超时，请稍后重试';
        }
        
        this.showError(errorMessage);
        
        // 重置重试计数（如果是认证错误）
        if (error.message.includes('401')) {
            this.retryCount = 0;
        }
    }
    
    // UI 操作方法
    showLoadingState() {
        const submitBtn = document.querySelector('#loginForm button[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<div class="loading"></div> 登录中...';
        }
        this.clearError();
    }
    
    hideLoadingState() {
        const submitBtn = document.querySelector('#loginForm button[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = false;
            submitBtn.innerHTML = '登录';
        }
    }
    
    showRetryState(retryCount) {
        const submitBtn = document.querySelector('#loginForm button[type="submit"]');
        if (submitBtn) {
            submitBtn.innerHTML = `<div class="loading"></div> 重试中 (${retryCount}/${this.maxRetries})...`;
        }
    }
    
    showError(message) {
        const errorDiv = document.getElementById('loginError');
        if (errorDiv) {
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }
    }
    
    clearError() {
        const errorDiv = document.getElementById('loginError');
        if (errorDiv) {
            errorDiv.style.display = 'none';
            errorDiv.textContent = '';
        }
    }
    
    showSuccessMessage(message) {
        if (window.notificationManager) {
            window.notificationManager.showSuccess(message);
        } else {
            console.log('✅ ' + message);
        }
    }
    
    showLocalModeNotification() {
        if (window.notificationManager) {
            window.notificationManager.showInfo('系统运行在本地模式，使用预设数据');
        }
    }
    
    hideLoginModal() {
        const modal = document.getElementById('loginModal');
        if (modal) {
            modal.style.display = 'none';
        }
    }
    
    showMainApp() {
        const mainApp = document.getElementById('mainApp');
        if (mainApp) {
            mainApp.classList.remove('hidden');
        }
    }
}

// 创建全局实例
window.enhancedLoginHandler = new EnhancedLoginHandler();

logger.info('模块', '增强登录处理器模块加载完成', { version: '1.0.0' });
