# 技术栈和架构

## 前端技术栈
- **核心语言**: JavaScript ES6+ (原生JavaScript，无框架依赖)
- **模块系统**: ES6 Modules + IIFE封装
- **构建方式**: 无构建步骤，直接运行 (支持file://协议)
- **样式**: CSS3 + CSS Grid + Flexbox
- **兼容性**: 现代浏览器 (Chrome 90+, Firefox 85+, Safari 14+)

## AI服务集成
- **主要LLM**: DeepSeek AI (api.deepseek.com)
- **备用LLM**: Google Gemini (generativelanguage.googleapis.com)  
- **图像处理**: Google Vision API (图片OCR识别)
- **故障切换**: 智能故障转移机制，30秒超时自动切换

## 外部API集成
- **订单管理**: GoMyHire API (https://gomyhire.com.my/api)
- **地址搜索**: Google Maps API (地址自动补全)
- **HTTP客户端**: Axios (CDN引入)

## 系统架构
```
前端应用 (纯JavaScript)
├── 核心层 (core/)
│   ├── 应用控制器 (app.js)
│   ├── 配置管理 (config.js) 
│   ├── 日志系统 (logger.js)
│   ├── 智能选择引擎 (smart-selection.js)
│   └── 性能优化器 (performance-optimizer.js)
├── 服务层 (services/)
│   ├── API服务 (api-service.js)
│   ├── LLM服务 (llm-service.js)
│   ├── 订单解析器 (order-parser.js)
│   └── 图像服务 (image-service.js)
├── 组件层 (components/)
│   └── 通知组件 (notification.js)
└── 资源层 (assets/)
    ├── 样式文件
    └── 图标资源
```

## 部署特点
- **无服务器依赖**: 纯前端应用，可直接在浏览器中运行
- **零配置部署**: 只需将文件放到Web服务器或直接用file://打开
- **CDN优化**: 外部依赖通过CDN加载，减少打包体积
- **渐进式加载**: 核心功能优先加载，非关键模块按需加载