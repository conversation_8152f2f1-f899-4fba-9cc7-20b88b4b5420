/**
 * @file module-loader.js - 模块懒加载管理器
 * @description 实现按需加载非关键模块，优化应用启动性能
 * <AUTHOR> IDE
 * @created_at 2025-01-06
 * @updated_at 2025-01-06
 * @version v4.1.0
 */

/**
 * @class ModuleLoader - 模块懒加载管理器
 * @description 管理模块的按需加载，支持预加载、缓存和错误处理
 */
class ModuleLoader {
    /**
     * @function constructor - 构造函数
     * @description 初始化模块加载器
     */
    constructor() {
        // 模块状态管理
        this.loadedModules = new Set(); // 已加载的模块
        this.loadingModules = new Map(); // 正在加载的模块 (Promise)
        this.failedModules = new Set(); // 加载失败的模块
        this.preloadQueue = new Set(); // 预加载队列
        
        // 模块配置
        this.moduleConfig = {
            // 错误处理模块（最高优先级）
            errorHandling: {
                scripts: [
                    'core/error-boundary.js'
                ],
                dependencies: ['logger'],
                priority: 'critical',
                preload: true,
                preloadDelay: 0, // 立即加载
                retryAttempts: 5,
                retryDelay: 500,
                essential: true // 标记为必需模块
            },
            
            // 智能选择模块
            smartSelection: {
                scripts: [
                    'core/smart-selection.js',
                    'core/smart-selection-engine.js',
                    'core/smart-selection/accuracy-calculator.js',
                    'core/smart-selection/api-sync-manager.js'
                ],
                dependencies: ['appState'],
                priority: 'high',
                preload: true, // 高优先级模块启用预加载
                preloadDelay: 2000, // 2秒后预加载
                retryAttempts: 3,
                retryDelay: 1000
            },
            
            // OTA Profile管理模块
            otaProfile: {
                scripts: [
                    'core/ota-profile-manager.js'
                ],
                dependencies: ['appState', 'SYSTEM_CONFIG'],
                priority: 'high',
                preload: true, // 高优先级模块启用预加载
                preloadDelay: 1500, // 1.5秒后预加载
                retryAttempts: 3,
                retryDelay: 1000
            },
            
            // 图像处理模块
            imageProcessing: {
                scripts: [
                    'services/image-service.js'
                ],
                dependencies: ['SYSTEM_CONFIG'],
                priority: 'medium',
                preload: false,
                retryAttempts: 2,
                retryDelay: 1500
            },
            
            // 地址搜索模块
            addressSearch: {
                scripts: [
                    'services/address-search-service.js'
                ],
                dependencies: ['SYSTEM_CONFIG'],
                priority: 'medium',
                preload: false,
                retryAttempts: 2,
                retryDelay: 1500
            }
        };
        
        // 加载统计
        this.loadStats = {
            totalRequests: 0,
            successfulLoads: 0,
            failedLoads: 0,
            cacheHits: 0,
            averageLoadTime: 0,
            preloadHits: 0,
            retryAttempts: 0
        };
        
        // 用户行为分析
        this.userBehavior = {
            lastActions: [],
            moduleUsageCount: new Map(),
            sessionStartTime: Date.now()
        };
        
        // 预加载状态
        this.preloadStatus = {
            enabled: true,
            inProgress: false,
            completedModules: new Set()
        };
        
        // 启动预加载策略
        this._initializePreloadStrategy();
        
        logger.info('ModuleLoader', '模块加载器初始化完成', {
            availableModules: Object.keys(this.moduleConfig),
            preloadEnabled: this.preloadStatus.enabled
        });
    }

    /**
     * @function _initializePreloadStrategy - 初始化预加载策略
     * @description 设置智能预加载机制
     */
    _initializePreloadStrategy() {
        // 延迟启动预加载，避免影响关键路径
        setTimeout(() => {
            this._startIntelligentPreload();
        }, 1000);
        
        // 监听用户交互，预测需要的模块
        this._setupUserBehaviorTracking();
    }

    /**
     * @function _startIntelligentPreload - 启动智能预加载
     * @description 根据配置和用户行为预加载模块
     */
    async _startIntelligentPreload() {
        if (!this.preloadStatus.enabled || this.preloadStatus.inProgress) {
            return;
        }
        
        this.preloadStatus.inProgress = true;
        logger.info('ModuleLoader', '启动智能预加载');
        
        // 按优先级排序模块
        const preloadModules = Object.entries(this.moduleConfig)
            .filter(([name, config]) => config.preload && !this.loadedModules.has(name))
            .sort(([, a], [, b]) => {
                const priorityOrder = { high: 3, medium: 2, low: 1 };
                return priorityOrder[b.priority] - priorityOrder[a.priority];
            });
        
        // 逐个预加载模块
        for (const [moduleName, moduleConfig] of preloadModules) {
            try {
                // 等待预加载延迟
                if (moduleConfig.preloadDelay) {
                    await this._delay(moduleConfig.preloadDelay);
                }
                
                // 检查是否已被用户请求加载
                if (this.loadedModules.has(moduleName)) {
                    continue;
                }
                
                logger.debug('ModuleLoader', '预加载模块', { moduleName });
                await this.loadModule(moduleName, { 
                    isPreload: true, 
                    showLoading: false 
                });
                
                this.preloadStatus.completedModules.add(moduleName);
                
            } catch (error) {
                logger.warn('ModuleLoader', '预加载失败', { 
                    moduleName, 
                    error: error.message 
                });
            }
        }
        
        this.preloadStatus.inProgress = false;
        logger.success('ModuleLoader', '智能预加载完成', {
            preloadedModules: Array.from(this.preloadStatus.completedModules)
        });
    }

    /**
     * @function _setupUserBehaviorTracking - 设置用户行为跟踪
     * @description 监听用户交互，预测模块需求
     */
    _setupUserBehaviorTracking() {
        // 监听文件上传相关事件
        document.addEventListener('click', (event) => {
            const target = event.target;
            
            // 图片相关操作
            if (target.matches('[data-tab="image"], #imageFile, .upload-area')) {
                this._recordUserAction('imageInteraction');
                this._predictModuleNeed('imageProcessing');
            }
            
            // 订单处理操作
            if (target.matches('#processBtn, #createOrderBtn')) {
                this._recordUserAction('orderProcessing');
                this._predictModuleNeed('smartSelection');
            }
        });
        
        // 监听输入框聚焦（地址搜索）
        document.addEventListener('focusin', (event) => {
            const target = event.target;
            if (target.matches('input[placeholder*="地址"], input[name*="pickup"], input[name*="destination"]')) {
                this._recordUserAction('addressInput');
                this._predictModuleNeed('addressSearch');
            }
        });
    }

    /**
     * @function _recordUserAction - 记录用户行为
     * @description 记录用户操作，用于预测分析
     * @param {string} action - 用户行为
     */
    _recordUserAction(action) {
        const timestamp = Date.now();
        this.userBehavior.lastActions.push({ action, timestamp });
        
        // 保持最近50个操作记录
        if (this.userBehavior.lastActions.length > 50) {
            this.userBehavior.lastActions.shift();
        }
    }

    /**
     * @function _predictModuleNeed - 预测模块需求
     * @description 根据用户行为预测并预加载模块
     * @param {string} moduleName - 模块名称
     */
    async _predictModuleNeed(moduleName) {
        // 如果模块已加载或正在加载，跳过
        if (this.loadedModules.has(moduleName) || this.loadingModules.has(moduleName)) {
            return;
        }
        
        // 增加模块使用计数
        const currentCount = this.userBehavior.moduleUsageCount.get(moduleName) || 0;
        this.userBehavior.moduleUsageCount.set(moduleName, currentCount + 1);
        
        // 如果用户频繁使用某个功能，立即预加载
        if (currentCount >= 2) {
            logger.info('ModuleLoader', '检测到频繁使用，立即预加载', { moduleName });
            try {
                await this.loadModule(moduleName, { 
                    isPreload: true, 
                    showLoading: false 
                });
            } catch (error) {
                logger.warn('ModuleLoader', '预测性预加载失败', { 
                    moduleName, 
                    error: error.message 
                });
            }
        }
    }

    /**
     * @function loadModule - 加载指定模块
     * @description 按需加载指定的模块，支持依赖检查和缓存
     * @param {string} moduleName - 模块名称
     * @param {object} options - 加载选项
     * @returns {Promise<boolean>} 加载结果
     */
    async loadModule(moduleName, options = {}) {
        const startTime = Date.now();
        this.loadStats.totalRequests++;
        
        // 检查模块是否已加载
        if (this.loadedModules.has(moduleName)) {
            // 区分缓存命中和预加载命中
            if (options.isPreload) {
                this.loadStats.preloadHits++;
                logger.debug('ModuleLoader', '模块已预加载（预加载命中）', { moduleName });
            } else {
                this.loadStats.cacheHits++;
                logger.debug('ModuleLoader', '模块已加载（缓存命中）', { moduleName });
            }
            return true;
        }
        
        // 检查模块是否正在加载
        if (this.loadingModules.has(moduleName)) {
            logger.debug('ModuleLoader', '模块正在加载，等待完成', { moduleName });
            return await this.loadingModules.get(moduleName);
        }
        
        // 检查模块配置
        const moduleConfig = this.moduleConfig[moduleName];
        if (!moduleConfig) {
            logger.error('ModuleLoader', '未知模块', { moduleName });
            this.loadStats.failedLoads++;
            return false;
        }
        
        // 创建加载Promise
        const loadPromise = this._loadModuleInternal(moduleName, moduleConfig, options, startTime);
        this.loadingModules.set(moduleName, loadPromise);
        
        try {
            const result = await loadPromise;
            this.loadingModules.delete(moduleName);
            return result;
        } catch (error) {
            this.loadingModules.delete(moduleName);
            throw error;
        }
    }

    /**
     * @function _loadModuleInternal - 内部模块加载逻辑
     * @description 执行实际的模块加载过程，支持重试机制
     * @param {string} moduleName - 模块名称
     * @param {object} moduleConfig - 模块配置
     * @param {object} options - 加载选项
     * @param {number} startTime - 开始时间
     * @returns {Promise<boolean>} 加载结果
     */
    async _loadModuleInternal(moduleName, moduleConfig, options, startTime) {
        const maxRetries = moduleConfig.retryAttempts || 1;
        let lastError = null;
        
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                if (attempt > 1) {
                    this.loadStats.retryAttempts++;
                    logger.info('ModuleLoader', '重试加载模块', { 
                        moduleName, 
                        attempt, 
                        maxRetries 
                    });
                    
                    // 重试延迟
                    await this._delay(moduleConfig.retryDelay || 1000);
                } else {
                    logger.info('ModuleLoader', '开始加载模块', { 
                        moduleName, 
                        scripts: moduleConfig.scripts.length,
                        priority: moduleConfig.priority,
                        isPreload: options.isPreload || false
                    });
                }
                
                // 检查依赖
                if (!this._checkDependencies(moduleConfig.dependencies)) {
                    throw new Error(`模块依赖不满足: ${moduleConfig.dependencies.join(', ')}`);
                }
                
                // 显示加载状态（如果需要）
                if (options.showLoading !== false && !options.isPreload) {
                    this._showLoadingStatus(moduleName, attempt, maxRetries);
                }
                
                // 加载脚本文件
                await this._loadScripts(moduleConfig.scripts);
                
                // 执行模块初始化
                await this._initializeModule(moduleName);
                
                // 标记为已加载
                this.loadedModules.add(moduleName);
                this.failedModules.delete(moduleName);
                
                // 更新统计
                const loadTime = Date.now() - startTime;
                this.loadStats.successfulLoads++;
                this._updateAverageLoadTime(loadTime);
                
                const logLevel = options.isPreload ? 'debug' : 'success';
                logger[logLevel]('ModuleLoader', '模块加载成功', { 
                    moduleName, 
                    loadTime: `${loadTime}ms`,
                    attempt: attempt > 1 ? attempt : undefined,
                    isPreload: options.isPreload || false
                });
                
                return true;
                
            } catch (error) {
                lastError = error;
                
                if (attempt < maxRetries) {
                    logger.warn('ModuleLoader', '模块加载失败，准备重试', { 
                        moduleName, 
                        attempt,
                        maxRetries,
                        error: error.message 
                    });
                } else {
                    this.failedModules.add(moduleName);
                    this.loadStats.failedLoads++;
                    
                    logger.error('ModuleLoader', '模块加载最终失败', { 
                        moduleName, 
                        totalAttempts: attempt,
                        error: error.message 
                    });
                }
            } finally {
                // 隐藏加载状态
                if (options.showLoading !== false && !options.isPreload) {
                    this._hideLoadingStatus();
                }
            }
        }
        
        // 如果是预加载失败，不影响用户体验
        if (options.isPreload) {
            logger.debug('ModuleLoader', '预加载失败，将在用户需要时重新尝试', { 
                moduleName 
            });
            return false;
        }
        
        // 用户主动请求的加载失败，提供降级方案
        return this._handleLoadFailure(moduleName, lastError);
    }

    /**
     * @function _handleLoadFailure - 处理加载失败
     * @description 提供加载失败的降级处理方案
     * @param {string} moduleName - 模块名称
     * @param {Error} error - 错误对象
     * @returns {boolean} 是否提供了降级方案
     */
    _handleLoadFailure(moduleName, error) {
        logger.error('ModuleLoader', '模块加载失败，尝试降级处理', { 
            moduleName, 
            error: error.message 
        });
        
        // 根据模块类型提供不同的降级方案
        switch (moduleName) {
            case 'smartSelection':
                // 智能选择失败，使用默认选择
                logger.warn('ModuleLoader', '智能选择不可用，将使用默认选择规则');
                this._createFallbackSmartSelection();
                return true;
                
            case 'imageProcessing':
                // 图像处理失败，提示用户手动输入
                logger.warn('ModuleLoader', '图像处理不可用，请手动输入订单信息');
                this._showImageProcessingFallback();
                return false;
                
            case 'addressSearch':
                // 地址搜索失败，使用基本输入框
                logger.warn('ModuleLoader', '地址搜索不可用，将使用基本地址输入');
                this._createFallbackAddressInput();
                return true;
                
            default:
                return false;
        }
    }

    /**
     * @function _createFallbackSmartSelection - 创建智能选择降级方案
     * @description 当智能选择模块加载失败时的降级处理
     */
    _createFallbackSmartSelection() {
        window.smartSelection = {
            selectCarType: () => ({ id: 1, confidence: 0.5, method: 'fallback' }),
            selectSubCategory: () => ({ id: 1, confidence: 0.5, method: 'fallback' }),
            selectBackendUser: () => ({ id: 1, confidence: 0.5, method: 'fallback' }),
            updateSystemData: () => {},
            resetToDefaults: () => {}
        };
    }

    /**
     * @function _showImageProcessingFallback - 显示图像处理降级提示
     * @description 当图像处理模块加载失败时的用户提示
     */
    _showImageProcessingFallback() {
        if (window.notificationManager) {
            window.notificationManager.warning(
                '图像处理不可用',
                '图像处理功能暂时不可用，请手动输入订单信息',
                8000
            );
        }
    }

    /**
     * @function _createFallbackAddressInput - 创建地址搜索降级方案
     * @description 当地址搜索模块加载失败时的降级处理
     */
    _createFallbackAddressInput() {
        window.addressSearchService = {
            initialize: () => {},
            searchAddressesWithAutocomplete: () => Promise.resolve([]),
            getPlaceDetailsWithCoordinates: () => Promise.resolve(null)
        };
    }

    /**
     * @function _delay - 延迟函数
     * @description 创建指定时间的延迟
     * @param {number} ms - 延迟毫秒数
     * @returns {Promise} 延迟Promise
     */
    _delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * @function _checkDependencies - 检查模块依赖
     * @description 检查模块的依赖是否已满足
     * @param {Array<string>} dependencies - 依赖列表
     * @returns {boolean} 依赖是否满足
     */
    _checkDependencies(dependencies) {
        if (!dependencies || dependencies.length === 0) {
            return true;
        }
        
        for (const dep of dependencies) {
            if (!window[dep]) {
                logger.warn('ModuleLoader', '依赖不满足', { dependency: dep });
                return false;
            }
        }
        
        return true;
    }

    /**
     * @function _loadScripts - 加载脚本文件
     * @description 动态加载JavaScript脚本文件
     * @param {Array<string>} scripts - 脚本文件路径列表
     * @returns {Promise<void>} 加载完成Promise
     */
    async _loadScripts(scripts) {
        const loadPromises = scripts.map(scriptPath => this._loadSingleScript(scriptPath));
        await Promise.all(loadPromises);
    }

    /**
     * @function _loadSingleScript - 加载单个脚本文件
     * @description 动态加载单个JavaScript脚本文件
     * @param {string} scriptPath - 脚本文件路径
     * @returns {Promise<void>} 加载完成Promise
     */
    _loadSingleScript(scriptPath) {
        return new Promise((resolve, reject) => {
            // 检查脚本是否已存在
            const existingScript = document.querySelector(`script[src="${scriptPath}"]`);
            if (existingScript) {
                resolve();
                return;
            }
            
            const script = document.createElement('script');
            script.src = scriptPath;
            script.async = true;
            
            script.onload = () => {
                logger.debug('ModuleLoader', '脚本加载成功', { scriptPath });
                resolve();
            };
            
            script.onerror = () => {
                logger.error('ModuleLoader', '脚本加载失败', { scriptPath });
                reject(new Error(`脚本加载失败: ${scriptPath}`));
            };
            
            document.head.appendChild(script);
        });
    }

    /**
     * @function _initializeModule - 初始化模块
     * @description 执行模块特定的初始化逻辑
     * @param {string} moduleName - 模块名称
     * @returns {Promise<void>} 初始化完成Promise
     */
    async _initializeModule(moduleName) {
        switch (moduleName) {
            case 'smartSelection':
                if (typeof initializeSmartSelection === 'function') {
                    await initializeSmartSelection();
                }
                break;
                
            case 'imageProcessing':
                if (window.ImageService && !window.imageService) {
                    window.imageService = new ImageService();
                }
                break;
                
            case 'addressSearch':
                if (window.AddressSearchService && !window.addressSearchService) {
                    window.addressSearchService = new AddressSearchService();
                    window.addressSearchService.initialize();
                }
                break;
                
            default:
                logger.debug('ModuleLoader', '无特定初始化逻辑', { moduleName });
        }
    }

    /**
     * @function _showLoadingStatus - 显示加载状态
     * @description 显示模块加载状态指示器，支持重试进度
     * @param {string} moduleName - 模块名称
     * @param {number} attempt - 当前尝试次数
     * @param {number} maxRetries - 最大重试次数
     */
    _showLoadingStatus(moduleName, attempt = 1, maxRetries = 1) {
        if (window.app && window.app.interfaceController) {
            const friendlyNames = {
                smartSelection: '智能选择',
                imageProcessing: '图像处理',
                addressSearch: '地址搜索'
            };
            
            const friendlyName = friendlyNames[moduleName] || moduleName;
            let message = `正在加载${friendlyName}模块...`;
            
            // 如果是重试，显示重试信息
            if (attempt > 1) {
                message += ` (重试 ${attempt}/${maxRetries})`;
            }
            
            window.app.interfaceController.showLoading(message);
        }
    }

    /**
     * @function _hideLoadingStatus - 隐藏加载状态
     * @description 隐藏模块加载状态指示器
     */
    _hideLoadingStatus() {
        if (window.app && window.app.interfaceController) {
            window.app.interfaceController.hideLoading();
        }
    }

    /**
     * @function _updateAverageLoadTime - 更新平均加载时间
     * @description 更新模块加载时间统计
     * @param {number} loadTime - 本次加载时间
     */
    _updateAverageLoadTime(loadTime) {
        const totalLoads = this.loadStats.successfulLoads;
        this.loadStats.averageLoadTime = 
            (this.loadStats.averageLoadTime * (totalLoads - 1) + loadTime) / totalLoads;
    }

    /**
     * @function getLoadStats - 获取加载统计
     * @description 获取模块加载的统计信息
     * @returns {object} 加载统计信息
     */
    getLoadStats() {
        const sessionTime = Date.now() - this.userBehavior.sessionStartTime;
        
        return {
            ...this.loadStats,
            loadedModules: Array.from(this.loadedModules),
            failedModules: Array.from(this.failedModules),
            loadingModules: Array.from(this.loadingModules.keys()),
            preloadedModules: Array.from(this.preloadStatus.completedModules),
            successRate: this.loadStats.totalRequests > 0 ? 
                (this.loadStats.successfulLoads / this.loadStats.totalRequests * 100).toFixed(2) + '%' : '0%',
            preloadHitRate: this.loadStats.totalRequests > 0 ?
                (this.loadStats.preloadHits / this.loadStats.totalRequests * 100).toFixed(2) + '%' : '0%',
            averageLoadTimeMs: Math.round(this.loadStats.averageLoadTime),
            sessionTimeMs: sessionTime,
            userBehavior: {
                totalActions: this.userBehavior.lastActions.length,
                moduleUsage: Object.fromEntries(this.userBehavior.moduleUsageCount),
                recentActions: this.userBehavior.lastActions.slice(-10)
            },
            preloadStatus: {
                enabled: this.preloadStatus.enabled,
                inProgress: this.preloadStatus.inProgress,
                completedCount: this.preloadStatus.completedModules.size
            }
        };
    }

    /**
     * @function getPerformanceReport - 获取性能报告
     * @description 生成详细的性能分析报告
     * @returns {object} 性能报告
     */
    getPerformanceReport() {
        const stats = this.getLoadStats();
        const sessionTimeMinutes = (stats.sessionTimeMs / 60000).toFixed(1);
        
        return {
            summary: {
                totalModules: Object.keys(this.moduleConfig).length,
                loadedModules: stats.loadedModules.length,
                successRate: stats.successRate,
                averageLoadTime: `${stats.averageLoadTimeMs}ms`,
                sessionTime: `${sessionTimeMinutes}分钟`
            },
            performance: {
                preloadEffectiveness: stats.preloadHitRate,
                retryRate: stats.retryAttempts > 0 ? 
                    ((stats.retryAttempts / stats.totalRequests) * 100).toFixed(2) + '%' : '0%',
                cacheEfficiency: stats.cacheHits > 0 ?
                    ((stats.cacheHits / stats.totalRequests) * 100).toFixed(2) + '%' : '0%'
            },
            userBehavior: {
                mostUsedFeatures: this._getMostUsedFeatures(),
                loadingPatterns: this._analyzeLoadingPatterns()
            },
            recommendations: this._generateOptimizationRecommendations(stats)
        };
    }

    /**
     * @function _getMostUsedFeatures - 获取最常用功能
     * @description 分析用户最常使用的功能模块
     * @returns {Array} 使用频率排序的功能列表
     */
    _getMostUsedFeatures() {
        return Array.from(this.userBehavior.moduleUsageCount.entries())
            .sort(([,a], [,b]) => b - a)
            .slice(0, 3)
            .map(([module, count]) => ({ module, count }));
    }

    /**
     * @function _analyzeLoadingPatterns - 分析加载模式
     * @description 分析用户的模块加载模式
     * @returns {object} 加载模式分析
     */
    _analyzeLoadingPatterns() {
        const recentActions = this.userBehavior.lastActions.slice(-20);
        const patterns = {};
        
        // 分析操作序列
        for (let i = 0; i < recentActions.length - 1; i++) {
            const current = recentActions[i].action;
            const next = recentActions[i + 1].action;
            const pattern = `${current} → ${next}`;
            patterns[pattern] = (patterns[pattern] || 0) + 1;
        }
        
        return {
            commonSequences: Object.entries(patterns)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 3)
                .map(([pattern, count]) => ({ pattern, count })),
            totalSequences: Object.keys(patterns).length
        };
    }

    /**
     * @function _generateOptimizationRecommendations - 生成优化建议
     * @description 基于使用统计生成优化建议
     * @param {object} stats - 统计数据
     * @returns {Array} 优化建议列表
     */
    _generateOptimizationRecommendations(stats) {
        const recommendations = [];
        
        // 预加载效果分析
        if (parseFloat(stats.preloadHitRate) < 30) {
            recommendations.push({
                type: 'preload',
                message: '预加载命中率较低，建议调整预加载策略',
                priority: 'medium'
            });
        }
        
        // 重试率分析
        if (stats.retryAttempts > stats.successfulLoads * 0.2) {
            recommendations.push({
                type: 'network',
                message: '模块加载重试率较高，建议检查网络连接',
                priority: 'high'
            });
        }
        
        // 用户行为分析
        const mostUsed = this._getMostUsedFeatures();
        if (mostUsed.length > 0 && mostUsed[0].count >= 5) {
            const topModule = mostUsed[0].module;
            if (!this.moduleConfig[topModule]?.preload) {
                recommendations.push({
                    type: 'preload',
                    message: `建议为常用功能 ${topModule} 启用预加载`,
                    priority: 'low'
                });
            }
        }
        
        return recommendations;
    }

    /**
     * @function isModuleLoaded - 检查模块是否已加载
     * @description 检查指定模块是否已成功加载
     * @param {string} moduleName - 模块名称
     * @returns {boolean} 是否已加载
     */
    isModuleLoaded(moduleName) {
        return this.loadedModules.has(moduleName);
    }

    // === 便捷方法 ===

    /**
     * @function loadSmartSelection - 加载智能选择模块
     * @description 便捷方法：加载智能选择模块
     * @returns {Promise<boolean>} 加载结果
     */
    async loadSmartSelection() {
        return await this.loadModule('smartSelection');
    }

    /**
     * @function loadImageProcessing - 加载图像处理模块
     * @description 便捷方法：加载图像处理模块
     * @returns {Promise<boolean>} 加载结果
     */
    async loadImageProcessing() {
        return await this.loadModule('imageProcessing');
    }

    /**
     * @function loadAddressSearch - 加载地址搜索模块
     * @description 便捷方法：加载地址搜索模块
     * @returns {Promise<boolean>} 加载结果
     */
    async loadAddressSearch() {
        return await this.loadModule('addressSearch');
    }

    /**
     * @function loadOTAProfile - 加载OTA Profile管理模块
     * @description 便捷方法：加载OTA Profile管理模块
     * @returns {Promise<boolean>} 加载结果
     */
    async loadOTAProfile() {
        return await this.loadModule('otaProfile');
    }
}

// 创建全局实例
window.moduleLoader = new ModuleLoader();

logger.info('模块', 'ModuleLoader模块加载器加载完成', { 
    version: 'v4.1.1',
    availableModules: Object.keys(window.moduleLoader.moduleConfig).length,
    features: ['智能预加载', '用户行为分析', '重试机制', '降级处理', '性能监控']
}); 