/**
 * @file logger.js - 日志同步调试控制台
 * <AUTHOR> IDE
 * @created_at 2024-12-19
 * @updated_at 2024-12-19
 */

// 日志级别枚举
const LOG_LEVELS = {
    DEBUG: { value: 0, name: 'DEBUG', color: '#6c757d', icon: '🔍' },
    INFO: { value: 1, name: 'INFO', color: '#17a2b8', icon: 'ℹ️' },
    WARN: { value: 2, name: 'WARN', color: '#ffc107', icon: '⚠️' },
    ERROR: { value: 3, name: 'ERROR', color: '#dc3545', icon: '❌' },
    SUCCESS: { value: 4, name: 'SUCCESS', color: '#28a745', icon: '✅' }
};

/**
 * @class Logger - 日志管理器
 * 提供统一的日志记录、显示和管理功能
 */
class Logger {
    constructor() {
        this.logs = [];
        this.maxLogs = 1000; // 最大日志条数

        // 安全地获取配置，避免依赖问题
        this.isProduction = false;
        try {
            this.isProduction = (typeof window !== 'undefined' &&
                               window.SYSTEM_CONFIG &&
                               window.SYSTEM_CONFIG.SYSTEM &&
                               window.SYSTEM_CONFIG.SYSTEM.PRODUCTION_MODE) || false;
        } catch (error) {
            console.warn('Logger: 无法获取SYSTEM_CONFIG，使用默认配置');
        }

        this.currentLevel = this.isProduction ? LOG_LEVELS.INFO.value : LOG_LEVELS.DEBUG.value;
        this.isConsoleVisible = false;
        this.autoScroll = true;
        this.filters = {
            module: '',
            timeRange: null
        };

        // 生产环境下不初始化控制台
        if (!this.isProduction) {
            // 延迟初始化控制台，确保DOM已加载
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.initConsole());
            } else {
                this.initConsole();
            }
        }

        // 监听全局错误
        this.setupGlobalErrorHandling();

        console.log('✅ Logger 已初始化');
    }

    /**
     * @function initConsole - 初始化调试控制台界面
     */
    initConsole() {
        // 创建控制台容器
        const consoleContainer = document.createElement('div');
        consoleContainer.id = 'debugConsole';
        consoleContainer.className = 'debug-console hidden';
        
        consoleContainer.innerHTML = `
            <div class="console-header">
                <div class="console-title">
                    <span class="console-icon">🖥️</span>
                    <span>调试控制台</span>
                    <span class="log-count">(0)</span>
                </div>
                <div class="console-controls">
                    <select id="logLevelFilter" class="level-filter">
                        <option value="0">DEBUG+</option>
                        <option value="1">INFO+</option>
                        <option value="2">WARN+</option>
                        <option value="3">ERROR+</option>
                    </select>
                    <input type="text" id="moduleFilter" placeholder="模块过滤..." class="module-filter">
                    <button id="clearLogs" class="btn-clear" title="清空日志">🗑️</button>
                    <button id="exportLogs" class="btn-export" title="导出日志">📥</button>
                    <button id="toggleAutoScroll" class="btn-scroll active" title="自动滚动">📜</button>
                    <button id="minimizeConsole" class="btn-minimize" title="最小化">➖</button>
                    <button id="closeConsole" class="btn-close" title="关闭">✖️</button>
                </div>
            </div>
            <div class="console-body">
                <div id="logContainer" class="log-container"></div>
            </div>
            <div class="console-footer">
                <div class="console-stats">
                    <span id="debugCount" class="stat-item">DEBUG: 0</span>
                    <span id="infoCount" class="stat-item">INFO: 0</span>
                    <span id="warnCount" class="stat-item">WARN: 0</span>
                    <span id="errorCount" class="stat-item">ERROR: 0</span>
                    <span id="successCount" class="stat-item">SUCCESS: 0</span>
                </div>
                <div class="console-actions">
                    <button id="pauseLogs" class="btn-pause">⏸️ 暂停</button>
                    <button id="resumeLogs" class="btn-resume hidden">▶️ 继续</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(consoleContainer);
        
        // 绑定事件
        this.bindConsoleEvents();
        
        // 创建控制台切换按钮
        this.createToggleButton();
    }

    /**
     * @function createToggleButton - 创建控制台切换按钮
     */
    createToggleButton() {
        const toggleBtn = document.createElement('button');
        toggleBtn.id = 'consoleToggle';
        toggleBtn.className = 'console-toggle';
        toggleBtn.innerHTML = '🖥️';
        toggleBtn.title = '打开调试控制台';
        
        toggleBtn.addEventListener('click', () => {
            this.toggleConsole();
        });
        
        document.body.appendChild(toggleBtn);
    }

    /**
     * @function bindConsoleEvents - 绑定控制台事件
     */
    bindConsoleEvents() {
        // 级别过滤
        document.getElementById('logLevelFilter').addEventListener('change', (e) => {
            this.currentLevel = parseInt(e.target.value);
            this.refreshDisplay();
        });
        
        // 模块过滤
        document.getElementById('moduleFilter').addEventListener('input', (e) => {
            this.filters.module = e.target.value.toLowerCase();
            this.refreshDisplay();
        });
        
        // 清空日志
        document.getElementById('clearLogs').addEventListener('click', () => {
            this.clearLogs();
        });
        
        // 导出日志
        document.getElementById('exportLogs').addEventListener('click', () => {
            this.exportLogs();
        });
        
        // 自动滚动切换
        document.getElementById('toggleAutoScroll').addEventListener('click', (e) => {
            this.autoScroll = !this.autoScroll;
            e.target.classList.toggle('active', this.autoScroll);
        });
        
        // 最小化
        document.getElementById('minimizeConsole').addEventListener('click', () => {
            this.minimizeConsole();
        });
        
        // 关闭
        document.getElementById('closeConsole').addEventListener('click', () => {
            this.hideConsole();
        });
        
        // 暂停/继续
        document.getElementById('pauseLogs').addEventListener('click', () => {
            this.pauseLogging();
        });
        
        document.getElementById('resumeLogs').addEventListener('click', () => {
            this.resumeLogging();
        });
    }

    /**
     * @function setupGlobalErrorHandling - 设置全局错误处理
     */
    setupGlobalErrorHandling() {
        // 捕获未处理的错误
        window.addEventListener('error', (event) => {
            this.error('全局错误', {
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                error: event.error
            });
        });
        
        // 捕获未处理的Promise拒绝
        window.addEventListener('unhandledrejection', (event) => {
            this.error('未处理的Promise拒绝', {
                reason: event.reason,
                promise: event.promise
            });
        });
    }

    /**
     * @function log - 通用日志记录方法
     * @param {string} level - 日志级别
     * @param {string} module - 模块名称
     * @param {string} message - 日志消息
     * @param {object} data - 附加数据
     */
    log(level, module, message, data = null) {
        if (this.isPaused) return;
        
        // 生产环境下跳过debug级别日志
        if (this.isProduction && level === 'debug') return;
        
        const logEntry = {
            id: Date.now() + Math.random(),
            timestamp: new Date(),
            level: level,
            module: module,
            message: message,
            data: data,
            stack: this.isProduction ? null : new Error().stack
        };
        
        this.logs.push(logEntry);
        
        // 限制日志数量
        if (this.logs.length > this.maxLogs) {
            this.logs.shift();
        }
        
        // 更新显示（生产环境下不显示）
        if (!this.isProduction) {
            this.addLogToDisplay(logEntry);
        }
        this.updateStats();
        
        // 同时输出到浏览器控制台
        this.outputToConsole(logEntry);
    }

    /**
     * @function debug - 调试级别日志
     * @param {string} module - 模块名称
     * @param {string} message - 日志消息
     * @param {object} data - 附加数据
     */
    debug(module, message, data = null) {
        this.log(LOG_LEVELS.DEBUG, module, message, data);
    }

    /**
     * @function info - 信息级别日志
     * @param {string} module - 模块名称
     * @param {string} message - 日志消息
     * @param {object} data - 附加数据
     */
    info(module, message, data = null) {
        this.log(LOG_LEVELS.INFO, module, message, data);
    }

    /**
     * @function warn - 警告级别日志
     * @param {string} module - 模块名称
     * @param {string} message - 日志消息
     * @param {object} data - 附加数据
     */
    warn(module, message, data = null) {
        this.log(LOG_LEVELS.WARN, module, message, data);
    }

    /**
     * @function error - 错误级别日志
     * @param {string} module - 模块名称
     * @param {string} message - 日志消息
     * @param {object} data - 附加数据
     */
    error(module, message, data = null) {
        this.log(LOG_LEVELS.ERROR, module, message, data);
    }

    /**
     * @function success - 成功级别日志
     * @param {string} module - 模块名称
     * @param {string} message - 日志消息
     * @param {object} data - 附加数据
     */
    success(module, message, data = null) {
        this.log(LOG_LEVELS.SUCCESS, module, message, data);
    }
    
        /**
         * @function performance - 性能级别日志
         * @param {string} module - 模块名称
         * @param {string} message - 日志消息
         * @param {object} data - 附加数据
         */
        performance(module, message, data = null) {
            // 为性能日志添加特殊样式和处理
            const performanceData = {
                type: 'PERFORMANCE_METRIC',
                timestamp: new Date().toISOString(),
                ...data
            };
            
            this.log(LOG_LEVELS.INFO, module, `🚀 ${message}`, performanceData);
        }
    
        /**
         * @function logPerformanceMetrics - 记录性能指标
         * @param {object} metrics - 性能指标对象
         */
        logPerformanceMetrics(metrics) {
            const {
                coreMetrics,
                memoryMetrics,
                apiMetrics,
                renderingMetrics,
                interactionMetrics,
                performanceScores,
                recommendations
            } = metrics;
    
            // 记录核心Web Vitals
            if (coreMetrics) {
                this.performance('性能监控-核心指标', '核心Web Vitals指标', {
                    firstContentfulPaint: coreMetrics.fcp,
                    largestContentfulPaint: coreMetrics.lcp,
                    firstInputDelay: coreMetrics.fid,
                    cumulativeLayoutShift: coreMetrics.cls,
                    category: 'web-vitals'
                });
            }
    
            // 记录内存使用情况
            if (memoryMetrics) {
                const level = parseFloat(memoryMetrics.usagePercent) > 75 ? LOG_LEVELS.WARN : LOG_LEVELS.INFO;
                this.log(level, '性能监控-内存', `内存使用: ${memoryMetrics.current} (${memoryMetrics.usagePercent})`, {
                    currentUsage: memoryMetrics.current,
                    peakUsage: memoryMetrics.peak,
                    usagePercent: memoryMetrics.usagePercent,
                    memoryLimit: memoryMetrics.limit,
                    category: 'memory'
                });
            }
    
            // 记录API性能
            if (apiMetrics && apiMetrics.totalRequests > 0) {
                const level = parseFloat(apiMetrics.errorRate) > 5 ? LOG_LEVELS.WARN : LOG_LEVELS.INFO;
                this.log(level, '性能监控-API', `API性能统计 (${apiMetrics.totalRequests}次请求)`, {
                    totalRequests: apiMetrics.totalRequests,
                    averageLatency: apiMetrics.avgLatency,
                    errorRate: apiMetrics.errorRate,
                    minLatency: apiMetrics.minLatency,
                    maxLatency: apiMetrics.maxLatency,
                    category: 'api-performance'
                });
            }
    
            // 记录渲染性能
            if (renderingMetrics) {
                const level = parseFloat(renderingMetrics.frameRate) < 55 ? LOG_LEVELS.WARN : LOG_LEVELS.INFO;
                this.log(level, '性能监控-渲染', `渲染性能: ${renderingMetrics.frameRate}`, {
                    frameRate: renderingMetrics.frameRate,
                    averageFrameTime: renderingMetrics.avgFrameTime,
                    droppedFrames: renderingMetrics.droppedFrames,
                    renderCalls: renderingMetrics.renderCalls,
                    category: 'rendering'
                });
            }
    
            // 记录交互性能
            if (interactionMetrics && interactionMetrics.totalInteractions > 0) {
                this.performance('性能监控-交互', `用户交互性能 (${interactionMetrics.totalInteractions}次交互)`, {
                    totalInteractions: interactionMetrics.totalInteractions,
                    avgClickLatency: interactionMetrics.avgClickLatency,
                    avgScrollLatency: interactionMetrics.avgScrollLatency,
                    avgInputLatency: interactionMetrics.avgInputLatency,
                    category: 'interaction'
                });
            }
    
            // 记录性能评分
            if (performanceScores) {
                const overallLevel = performanceScores.overall >= 75 ? LOG_LEVELS.SUCCESS : 
                                   performanceScores.overall >= 50 ? LOG_LEVELS.WARN : LOG_LEVELS.ERROR;
                
                this.log(overallLevel, '性能监控-评分', `综合性能评分: ${performanceScores.overall}/100`, {
                    overallScore: performanceScores.overall,
                    fcpScore: performanceScores.fcp,
                    lcpScore: performanceScores.lcp,
                    fidScore: performanceScores.fid,
                    clsScore: performanceScores.cls,
                    category: 'performance-score'
                });
            }
    
            // 记录优化建议
            if (recommendations && recommendations.length > 0) {
                this.info('性能监控-建议', `性能优化建议 (${recommendations.length}条)`, {
                    recommendations: recommendations,
                    category: 'optimization-suggestions'
                });
            }
        }
    
        /**
         * @function logMemoryWarning - 记录内存警告
         * @param {object} memoryInfo - 内存信息
         * @param {string} warningType - 警告类型
         */
        logMemoryWarning(memoryInfo, warningType = 'high') {
            const level = warningType === 'critical' ? LOG_LEVELS.ERROR : LOG_LEVELS.WARN;
            const message = warningType === 'critical' ? '内存使用率过高，可能影响应用稳定性' : '内存使用率偏高，建议优化';
            
            this.log(level, '内存监控', message, {
                type: 'MEMORY_WARNING',
                warningType: warningType,
                ...memoryInfo,
                recommendations: this.getMemoryOptimizationTips(warningType)
            });
        }
    
        /**
         * @function getMemoryOptimizationTips - 获取内存优化建议
         * @param {string} warningType - 警告类型
         * @returns {array} 优化建议数组
         */
        getMemoryOptimizationTips(warningType) {
            const basicTips = [
                '清理不必要的变量引用',
                '检查是否有内存泄漏',
                '优化图片和资源大小',
                '使用对象池减少垃圾回收'
            ];
    
            const criticalTips = [
                '立即释放大型对象',
                '清空缓存和临时数据',
                '停止不必要的定时器',
                '考虑页面刷新重置状态'
            ];
    
            return warningType === 'critical' ? [...basicTips, ...criticalTips] : basicTips;
        }
    
        /**
         * @function logApiPerformanceAlert - 记录API性能警报
         * @param {string} url - API地址
         * @param {number} latency - 延迟时间
         * @param {string} alertType - 警报类型
         */
        logApiPerformanceAlert(url, latency, alertType = 'slow') {
            const level = alertType === 'timeout' ? LOG_LEVELS.ERROR : LOG_LEVELS.WARN;
            const message = alertType === 'timeout' ? 'API请求超时' : 'API响应缓慢';
            
            this.log(level, 'API性能警报', message, {
                type: 'API_PERFORMANCE_ALERT',
                url: url,
                latency: `${latency.toFixed(2)}ms`,
                alertType: alertType,
                threshold: alertType === 'timeout' ? '5000ms' : '2000ms',
                recommendations: [
                    '检查网络连接状态',
                    '验证服务器性能',
                    '考虑使用缓存策略',
                    '优化请求参数和数据量'
                ]
            });
        }
    
        /**
         * @function startPerformanceLogging - 启动性能日志记录
         */
        startPerformanceLogging() {
            if (window.performanceMonitor) {
                this.info('性能监控', '性能日志记录已启动，将定期收集和报告性能指标');
                
                // 监听性能监控器的事件（如果有的话）
                this.isPerformanceLoggingActive = true;
            } else {
                this.warn('性能监控', '性能监控器未初始化，无法启动性能日志记录');
            }
        }
    
        /**
         * @function stopPerformanceLogging - 停止性能日志记录
         */
        stopPerformanceLogging() {
            this.isPerformanceLoggingActive = false;
            this.info('性能监控', '性能日志记录已停止');
        }
    /**
     * @function logApiRequest - 记录API请求日志
     * @param {string} method - HTTP方法
     * @param {string} url - 请求URL
     * @param {object} requestData - 请求数据
     * @param {object} headers - 请求头
     */
    logApiRequest(method, url, requestData = null, headers = null) {
        const apiRequestInfo = {
            method: method,
            url: url,
            timestamp: new Date().toISOString(),
            headers: headers,
            requestData: requestData
        };

        this.info('API请求', `${method} ${url}`, {
            type: 'API_REQUEST',
            ...apiRequestInfo
        });
    }

    /**
     * @function logApiResponse - 记录API响应日志
     * @param {string} method - HTTP方法
     * @param {string} url - 请求URL
     * @param {number} status - 响应状态码
     * @param {object} responseData - 响应数据
     * @param {number} responseTime - 响应时间（毫秒）
     */
    logApiResponse(method, url, status, responseData = null, responseTime = null) {
        const apiResponseInfo = {
            method: method,
            url: url,
            status: status,
            timestamp: new Date().toISOString(),
            responseTime: responseTime,
            responseData: responseData
        };

        const level = status >= 200 && status < 300 ? LOG_LEVELS.SUCCESS : LOG_LEVELS.ERROR;
        const message = `${method} ${url} - ${status} ${responseTime ? `(${responseTime}ms)` : ''}`;

        this.log(level, 'API响应', message, {
            type: 'API_RESPONSE',
            ...apiResponseInfo
        });
    }

    /**
     * @function formatApiResponse - 格式化API响应数据
     * @param {object} responseData - 响应数据
     * @returns {string} 格式化后的响应数据
     */
    formatApiResponse(responseData) {
        if (!responseData) return '';

        try {
            return JSON.stringify(responseData, null, 2);
        } catch (error) {
            return String(responseData);
        }
    }

    /**
     * @function addLogToDisplay - 添加日志到显示区域
     * @param {object} logEntry - 日志条目
     */
    addLogToDisplay(logEntry) {
        if (this.isProduction || !this.shouldDisplayLog(logEntry)) return;
        
        const container = document.getElementById('logContainer');
        if (!container) return;
        
        const logElement = this.createLogElement(logEntry);
        container.appendChild(logElement);
        
        // 减少自动滚动频率，提升性能
        if (this.autoScroll && this.logs.length % 3 === 0) {
            container.scrollTop = container.scrollHeight;
        }
        
        // 限制显示的日志数量
        const maxDisplayLogs = 500;
        while (container.children.length > maxDisplayLogs) {
            container.removeChild(container.firstChild);
        }
    }

    /**
     * @function createLogElement - 创建日志元素
     * @param {object} logEntry - 日志条目
     * @returns {HTMLElement} 日志元素
     */
    createLogElement(logEntry) {
        const logDiv = document.createElement('div');
        logDiv.className = `log-entry log-${logEntry.level.name.toLowerCase()}`;
        logDiv.dataset.logId = logEntry.id;

        const timeStr = logEntry.timestamp.toLocaleTimeString('zh-CN', {
            hour12: false,
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            fractionalSecondDigits: 3
        });

        // 检查是否为API日志，需要特殊处理
        const isApiLog = logEntry.data && (logEntry.data.type === 'API_REQUEST' || logEntry.data.type === 'API_RESPONSE');

        logDiv.innerHTML = `
            <div class="log-header">
                <span class="log-icon">${logEntry.level.icon}</span>
                <span class="log-time">${timeStr}</span>
                <span class="log-level">${logEntry.level.name}</span>
                <span class="log-module">[${logEntry.module}]</span>
                ${isApiLog ? '<span class="log-expand-hint">点击展开详情</span>' : ''}
            </div>
            <div class="log-message">${this.escapeHtml(logEntry.message)}</div>
            ${logEntry.data ? `<div class="log-data ${isApiLog ? 'api-data' : ''}">${this.formatData(logEntry.data)}</div>` : ''}
        `;

        // 为API日志添加特殊的展开/收起功能
        if (isApiLog) {
            const dataDiv = logDiv.querySelector('.log-data');
            dataDiv.style.display = 'none'; // 默认收起

            logDiv.addEventListener('click', () => {
                const isExpanded = dataDiv.style.display !== 'none';
                dataDiv.style.display = isExpanded ? 'none' : 'block';
                logDiv.classList.toggle('expanded', !isExpanded);

                const hint = logDiv.querySelector('.log-expand-hint');
                if (hint) {
                    hint.textContent = isExpanded ? '点击展开详情' : '点击收起详情';
                }
            });
        } else {
            // 普通日志的展开/收起功能
            logDiv.addEventListener('click', () => {
                logDiv.classList.toggle('expanded');
            });
        }

        return logDiv;
    }

    /**
     * @function shouldDisplayLog - 判断是否应该显示日志
     * @param {object} logEntry - 日志条目
     * @returns {boolean} 是否显示
     */
    shouldDisplayLog(logEntry) {
        // 级别过滤
        if (logEntry.level.value < this.currentLevel) {
            return false;
        }
        
        // 模块过滤
        if (this.filters.module && !logEntry.module.toLowerCase().includes(this.filters.module)) {
            return false;
        }
        
        return true;
    }

    /**
     * @function formatData - 格式化附加数据
     * @param {any} data - 数据
     * @returns {string} 格式化后的字符串
     */
    formatData(data) {
        if (data === null || data === undefined) {
            return '';
        }
        
        if (typeof data === 'string') {
            return this.escapeHtml(data);
        }
        
        try {
            return `<pre>${this.escapeHtml(JSON.stringify(data, null, 2))}</pre>`;
        } catch (e) {
            return this.escapeHtml(String(data));
        }
    }

    /**
     * @function escapeHtml - 转义HTML字符
     * @param {string} text - 文本
     * @returns {string} 转义后的文本
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * @function outputToConsole - 输出到浏览器控制台
     * @param {object} logEntry - 日志条目
     */
    outputToConsole(logEntry) {
        const message = `[${logEntry.module}] ${logEntry.message}`;
        
        switch (logEntry.level.name) {
            case 'DEBUG':
                console.debug(message, logEntry.data);
                break;
            case 'INFO':
                console.info(message, logEntry.data);
                break;
            case 'WARN':
                console.warn(message, logEntry.data);
                break;
            case 'ERROR':
                console.error(message, logEntry.data);
                break;
            case 'SUCCESS':
                console.log(`%c${message}`, 'color: #28a745; font-weight: bold;', logEntry.data);
                break;
        }
    }

    /**
     * @function updateStats - 更新统计信息
     */
        updateStats() {
            if (this.isProduction) return;
            
            const stats = {
                DEBUG: 0,
                INFO: 0,
                WARN: 0,
                ERROR: 0,
                SUCCESS: 0
            };
            
            // 性能相关日志统计
            const performanceStats = {
                total: 0,
                memory: 0,
                api: 0,
                rendering: 0,
                interaction: 0
            };
            
            this.logs.forEach(log => {
                stats[log.level.name]++;
                
                // 统计性能相关日志
                if (log.data && log.data.type === 'PERFORMANCE_METRIC') {
                    performanceStats.total++;
                    
                    if (log.data.category) {
                        switch (log.data.category) {
                            case 'memory':
                                performanceStats.memory++;
                                break;
                            case 'api-performance':
                                performanceStats.api++;
                                break;
                            case 'rendering':
                                performanceStats.rendering++;
                                break;
                            case 'interaction':
                                performanceStats.interaction++;
                                break;
                        }
                    }
                }
            });
            
            const debugCount = document.getElementById('debugCount');
            const infoCount = document.getElementById('infoCount');
            const warnCount = document.getElementById('warnCount');
            const errorCount = document.getElementById('errorCount');
            const successCount = document.getElementById('successCount');
            const logCount = document.querySelector('.log-count');
            
            if (debugCount) debugCount.textContent = `DEBUG: ${stats.DEBUG}`;
            if (infoCount) infoCount.textContent = `INFO: ${stats.INFO}`;
            if (warnCount) warnCount.textContent = `WARN: ${stats.WARN}`;
            if (errorCount) errorCount.textContent = `ERROR: ${stats.ERROR}`;
            if (successCount) successCount.textContent = `SUCCESS: ${stats.SUCCESS}`;
            
            const totalCount = Object.values(stats).reduce((a, b) => a + b, 0);
            if (logCount) logCount.textContent = `(${totalCount})`;
            
            // 更新性能统计显示（如果存在）
            this.updatePerformanceStats(performanceStats);
        }
        
            /**
             * @function updatePerformanceStats - 更新性能统计显示
             * @param {object} performanceStats - 性能统计数据
             */
            updatePerformanceStats(performanceStats) {
                // 检查是否存在性能统计显示区域
                let perfStatsElement = document.getElementById('performanceStats');
                
                if (!perfStatsElement && performanceStats.total > 0) {
                    // 如果不存在且有性能数据，创建性能统计显示
                    perfStatsElement = document.createElement('div');
                    perfStatsElement.id = 'performanceStats';
                    perfStatsElement.className = 'performance-stats';
                    
                    const footer = document.querySelector('.console-footer .console-stats');
                    if (footer) {
                        footer.appendChild(perfStatsElement);
                    }
                }
                
                if (perfStatsElement) {
                    perfStatsElement.innerHTML = `
                        <span class="perf-stat-item" title="性能日志总数">⚡ 性能: ${performanceStats.total}</span>
                        ${performanceStats.memory > 0 ? `<span class="perf-stat-item" title="内存监控">🧠 ${performanceStats.memory}</span>` : ''}
                        ${performanceStats.api > 0 ? `<span class="perf-stat-item" title="API性能">🌐 ${performanceStats.api}</span>` : ''}
                        ${performanceStats.rendering > 0 ? `<span class="perf-stat-item" title="渲染性能">🎨 ${performanceStats.rendering}</span>` : ''}
                        ${performanceStats.interaction > 0 ? `<span class="perf-stat-item" title="交互性能">👆 ${performanceStats.interaction}</span>` : ''}
                    `;
                    
                    // 如果没有性能数据，隐藏元素
                    perfStatsElement.style.display = performanceStats.total > 0 ? 'block' : 'none';
                }
            }

    /**
     * @function refreshDisplay - 刷新显示
     */
    refreshDisplay() {
        const container = document.getElementById('logContainer');
        container.innerHTML = '';
        
        this.logs.forEach(log => {
            this.addLogToDisplay(log);
        });
    }

    /**
     * @function clearLogs - 清空日志
     */
    clearLogs() {
        this.logs = [];
        document.getElementById('logContainer').innerHTML = '';
        this.updateStats();
        this.info('系统', '日志已清空');
    }

    /**
     * @function exportLogs - 导出日志
     */
    exportLogs() {
        const exportData = {
            exportTime: new Date().toISOString(),
            totalLogs: this.logs.length,
            logs: this.logs.map(log => ({
                timestamp: log.timestamp.toISOString(),
                level: log.level.name,
                module: log.module,
                message: log.message,
                data: log.data
            }))
        };
        
        const blob = new Blob([JSON.stringify(exportData, null, 2)], {
            type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `debug-logs-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.success('系统', '日志导出完成');
    }

    /**
     * @function toggleConsole - 切换控制台显示
     */
    toggleConsole() {
        if (this.isConsoleVisible) {
            this.hideConsole();
        } else {
            this.showConsole();
        }
    }

    /**
     * @function showConsole - 显示控制台
     */
    showConsole() {
        document.getElementById('debugConsole').classList.remove('hidden');
        this.isConsoleVisible = true;
        document.getElementById('consoleToggle').style.display = 'none';
    }

    /**
     * @function hideConsole - 隐藏控制台
     */
    hideConsole() {
        document.getElementById('debugConsole').classList.add('hidden');
        this.isConsoleVisible = false;
        document.getElementById('consoleToggle').style.display = 'block';
    }

    /**
     * @function minimizeConsole - 最小化控制台
     */
    minimizeConsole() {
        const console = document.getElementById('debugConsole');
        console.classList.toggle('minimized');
    }

    /**
     * @function pauseLogging - 暂停日志记录
     */
    pauseLogging() {
        this.isPaused = true;
        document.getElementById('pauseLogs').classList.add('hidden');
        document.getElementById('resumeLogs').classList.remove('hidden');
        this.warn('系统', '日志记录已暂停');
    }

    /**
     * @function resumeLogging - 恢复日志记录
     */
    resumeLogging() {
        this.isPaused = false;
        document.getElementById('pauseLogs').classList.remove('hidden');
        document.getElementById('resumeLogs').classList.add('hidden');
        this.info('系统', '日志记录已恢复');
    }

    /**
     * @function getLogStats - 获取日志统计信息
     * @returns {object} 统计信息
     */
    getLogStats() {
        const stats = {
            total: this.logs.length,
            byLevel: {},
            byModule: {},
            timeRange: {
                start: this.logs.length > 0 ? this.logs[0].timestamp : null,
                end: this.logs.length > 0 ? this.logs[this.logs.length - 1].timestamp : null
            }
        };
        
        this.logs.forEach(log => {
            // 按级别统计
            stats.byLevel[log.level.name] = (stats.byLevel[log.level.name] || 0) + 1;
            
            // 按模块统计
            stats.byModule[log.module] = (stats.byModule[log.module] || 0) + 1;
        });
        
        return stats;
    }
}

// 创建全局日志实例
const logger = new Logger();

// 导出日志实例到全局作用域（浏览器环境）
if (typeof window !== 'undefined') {
    window.logger = logger;
    console.log('✅ logger 已导出到全局作用域');
}

// 导出日志实例（Node.js模块环境）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = logger;
}