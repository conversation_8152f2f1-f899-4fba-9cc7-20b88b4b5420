/**
 * @file performance-monitor.js - 性能监控工具
 * @description 实时监控页面性能指标，提供优化建议
 * <AUTHOR>
 * @version 1.0.0
 */

class PerformanceMonitor {
    constructor() {
        this.metrics = {
            // 现有指标
            loadTime: 0,
            domContentLoaded: 0,
            firstPaint: 0,
            firstContentfulPaint: 0,
            largestContentfulPaint: 0,
            firstInputDelay: 0,
            cumulativeLayoutShift: 0,
            resourceLoadTimes: new Map(),
            scriptLoadTimes: new Map(),
            
            // 新增增强指标
            memoryUsage: null,
            realTimeMemory: {
                current: 0,
                peak: 0,
                average: 0,
                samples: []
            },
            apiMetrics: {
                totalRequests: 0,
                averageLatency: 0,
                minLatency: Infinity,
                maxLatency: 0,
                errorRate: 0,
                requestsByStatus: new Map(),
                latencyHistory: []
            },
            renderingMetrics: {
                frameRate: 0,
                averageFrameTime: 0,
                droppedFrames: 0,
                renderCalls: 0,
                paintTime: 0,
                layoutTime: 0
            },
            userInteractionMetrics: {
                clickLatency: [],
                scrollLatency: [],
                inputLatency: [],
                totalInteractions: 0
            }
        };
        
        this.observers = [];
        this.startTime = performance.now();
        this.isMonitoring = true;
        this.reportInterval = null;
        
        // 内存监控定时器
        this.memoryMonitorInterval = null;
        
        // 渲染性能监控
        this.frameCount = 0;
        this.lastFrameTime = performance.now();
        this.animationFrameId = null;
        
        this.init();
    }
    
    /**
     * @function init - 初始化性能监控
     * @description 设置各种性能观察器和事件监听器
     */
        init() {
            this.observeNavigationTiming();
            this.observePaintTiming();
            this.observeLargestContentfulPaint();
            this.observeFirstInputDelay();
            this.observeCumulativeLayoutShift();
            this.observeResourceTiming();
            
            // 新增监控初始化
            this.initMemoryMonitoring();
            this.initApiMonitoring();
            this.initRenderingMonitoring();
            this.initUserInteractionMonitoring();
            
            // 启动logger的性能日志记录
            if (window.logger && typeof window.logger.startPerformanceLogging === 'function') {
                window.logger.startPerformanceLogging();
            }
            
            // 启动定期报告
            this.startPeriodicReporting();
            
            // 页面卸载时生成最终报告
            window.addEventListener('beforeunload', () => {
                this.generateFinalReport();
            });
            
            // 页面可见性变化时的处理
            document.addEventListener('visibilitychange', () => {
                if (document.hidden) {
                    this.pauseMonitoring();
                } else {
                    this.resumeMonitoring();
                }
            });
            
            // 初始化完成日志
            if (window.logger) {
                window.logger.success('性能监控', '增强性能监控已初始化', {
                    features: [
                        '内存使用监控 (每5秒)',
                        'API延迟追踪 (fetch/xhr)',
                        '渲染性能监控 (帧率/掉帧)',
                        '用户交互延迟 (点击/滚动/输入)',
                        '定期性能报告 (每30秒)',
                        '智能性能警告'
                    ],
                    reportingInterval: '30秒',
                    memoryMonitoringInterval: '5秒'
                });
            }
        }

    
    /**
     * @function initMemoryMonitoring - 初始化内存监控
     */
    initMemoryMonitoring() {
        if ('memory' in performance) {
            // 立即获取初始内存信息
            this.updateMemoryMetrics();
            
            // 每5秒监控一次内存使用
            this.memoryMonitorInterval = setInterval(() => {
                this.updateMemoryMetrics();
            }, 5000);
        }
    }
    
    /**
     * @function updateMemoryMetrics - 更新内存指标
     */
    updateMemoryMetrics() {
        if ('memory' in performance) {
            const memory = performance.memory;
            const currentUsed = memory.usedJSHeapSize;
            
            this.metrics.memoryUsage = {
                used: currentUsed,
                total: memory.totalJSHeapSize,
                limit: memory.jsHeapSizeLimit,
                usagePercent: (currentUsed / memory.jsHeapSizeLimit * 100).toFixed(2)
            };
            
            // 更新实时内存统计
            const realTime = this.metrics.realTimeMemory;
            realTime.current = currentUsed;
            realTime.peak = Math.max(realTime.peak, currentUsed);
            
            // 保持最近100个样本
            realTime.samples.push({
                timestamp: Date.now(),
                value: currentUsed
            });
            
            if (realTime.samples.length > 100) {
                realTime.samples.shift();
            }
            
            // 计算平均值
            realTime.average = realTime.samples.reduce((sum, sample) => sum + sample.value, 0) / realTime.samples.length;
            
            // 内存警告检查
            this.checkMemoryWarnings();
        }
    }
    
    /**
     * @function checkMemoryWarnings - 检查内存警告
     */
        checkMemoryWarnings() {
            const usage = this.metrics.memoryUsage;
            if (usage && window.logger) {
                const usagePercent = parseFloat(usage.usagePercent);
                
                if (usagePercent > 90) {
                    // 使用logger的专门内存警告方法
                    window.logger.logMemoryWarning(usage, 'critical');
                } else if (usagePercent > 75) {
                    window.logger.logMemoryWarning(usage, 'high');
                }
            }
        }

    
    /**
     * @function initApiMonitoring - 初始化API监控
     */
    initApiMonitoring() {
        // 拦截 fetch API
        this.interceptFetch();
        
        // 拦截 XMLHttpRequest
        this.interceptXHR();
    }
    
    /**
     * @function interceptFetch - 拦截 fetch API
     */
    interceptFetch() {
        const originalFetch = window.fetch;
        const self = this;
        
        window.fetch = function(...args) {
            const startTime = performance.now();
            const url = args[0];
            
            return originalFetch.apply(this, args)
                .then(response => {
                    const endTime = performance.now();
                    const latency = endTime - startTime;
                    
                    self.recordApiMetrics(url, response.status, latency, 'fetch');
                    return response;
                })
                .catch(error => {
                    const endTime = performance.now();
                    const latency = endTime - startTime;
                    
                    self.recordApiMetrics(url, 0, latency, 'fetch', error);
                    throw error;
                });
        };
    }
    
    /**
     * @function interceptXHR - 拦截 XMLHttpRequest
     */
    interceptXHR() {
        const originalOpen = XMLHttpRequest.prototype.open;
        const originalSend = XMLHttpRequest.prototype.send;
        const self = this;
        
        XMLHttpRequest.prototype.open = function(method, url, ...args) {
            this._performanceMonitor = {
                url: url,
                method: method,
                startTime: null
            };
            return originalOpen.apply(this, [method, url, ...args]);
        };
        
        XMLHttpRequest.prototype.send = function(...args) {
            if (this._performanceMonitor) {
                this._performanceMonitor.startTime = performance.now();
                
                const originalOnLoad = this.onload;
                const originalOnError = this.onerror;
                
                this.onload = function(...loadArgs) {
                    const endTime = performance.now();
                    const latency = endTime - this._performanceMonitor.startTime;
                    
                    self.recordApiMetrics(
                        this._performanceMonitor.url,
                        this.status,
                        latency,
                        'xhr'
                    );
                    
                    if (originalOnLoad) {
                        return originalOnLoad.apply(this, loadArgs);
                    }
                };
                
                this.onerror = function(...errorArgs) {
                    const endTime = performance.now();
                    const latency = endTime - this._performanceMonitor.startTime;
                    
                    self.recordApiMetrics(
                        this._performanceMonitor.url,
                        this.status || 0,
                        latency,
                        'xhr',
                        new Error('XHR Error')
                    );
                    
                    if (originalOnError) {
                        return originalOnError.apply(this, errorArgs);
                    }
                };
            }
            
            return originalSend.apply(this, args);
        };
    }
    
    /**
     * @function recordApiMetrics - 记录API指标
     * @param {string} url - 请求URL
     * @param {number} status - 响应状态码
     * @param {number} latency - 延迟时间（毫秒）
     * @param {string} type - 请求类型（fetch/xhr）
     * @param {Error} error - 错误对象（如果有）
     */
        recordApiMetrics(url, status, latency, type, error = null) {
            const api = this.metrics.apiMetrics;
            
            api.totalRequests++;
            
            // 更新延迟统计
            api.minLatency = Math.min(api.minLatency, latency);
            api.maxLatency = Math.max(api.maxLatency, latency);
            
            // 记录延迟历史（保持最近100次请求）
            api.latencyHistory.push({
                url: url,
                latency: latency,
                status: status,
                timestamp: Date.now(),
                type: type,
                error: error ? error.message : null
            });
            
            if (api.latencyHistory.length > 100) {
                api.latencyHistory.shift();
            }
            
            // 计算平均延迟
            api.averageLatency = api.latencyHistory.reduce((sum, record) => sum + record.latency, 0) / api.latencyHistory.length;
            
            // 统计状态码
            const statusKey = error ? 'error' : status.toString();
            api.requestsByStatus.set(statusKey, (api.requestsByStatus.get(statusKey) || 0) + 1);
            
            // 计算错误率
            const errorCount = api.requestsByStatus.get('error') || 0;
            api.errorRate = (errorCount / api.totalRequests * 100).toFixed(2);
            
            // 记录到日志
            if (window.logger) {
                const level = error || status >= 400 ? 'warn' : (latency > 2000 ? 'warn' : 'debug');
                window.logger[level]('API监控', `${type.toUpperCase()} ${url}`, {
                    status: status,
                    latency: `${latency.toFixed(2)}ms`,
                    error: error ? error.message : null,
                    totalRequests: api.totalRequests,
                    avgLatency: `${api.averageLatency.toFixed(2)}ms`,
                    errorRate: `${api.errorRate}%`
                });
            }
            
            // 性能警告 - 使用logger的专门API性能警报方法
            if (window.logger) {
                if (latency > 5000) {
                    window.logger.logApiPerformanceAlert(url, latency, 'timeout');
                } else if (latency > 2000) {
                    window.logger.logApiPerformanceAlert(url, latency, 'slow');
                }
            }
        }

    
    /**
     * @function initRenderingMonitoring - 初始化渲染监控
     */
    initRenderingMonitoring() {
        // 使用 requestAnimationFrame 监控帧率
        this.monitorFrameRate();
        
        // 监控渲染性能
        if ('PerformanceObserver' in window) {
            this.observeRenderingMetrics();
        }
    }
    
    /**
     * @function monitorFrameRate - 监控帧率
     */
    monitorFrameRate() {
        const measureFrame = (currentTime) => {
            if (this.lastFrameTime) {
                const frameDuration = currentTime - this.lastFrameTime;
                this.frameCount++;
                
                // 更新渲染指标
                const rendering = this.metrics.renderingMetrics;
                rendering.averageFrameTime = ((rendering.averageFrameTime * (this.frameCount - 1)) + frameDuration) / this.frameCount;
                rendering.frameRate = 1000 / rendering.averageFrameTime;
                
                // 检测掉帧（超过16.67ms为掉帧）
                if (frameDuration > 16.67) {
                    rendering.droppedFrames++;
                }
            }
            
            this.lastFrameTime = currentTime;
            
            if (this.isMonitoring) {
                this.animationFrameId = requestAnimationFrame(measureFrame);
            }
        };
        
        this.animationFrameId = requestAnimationFrame(measureFrame);
    }
    
    /**
     * @function observeRenderingMetrics - 观察渲染指标
     */
    observeRenderingMetrics() {
        // 观察 measure 性能条目
        const measureObserver = new PerformanceObserver((list) => {
            for (const entry of list.getEntries()) {
                if (entry.name.includes('paint') || entry.name.includes('render')) {
                    this.metrics.renderingMetrics.paintTime += entry.duration;
                    this.metrics.renderingMetrics.renderCalls++;
                }
                
                if (entry.name.includes('layout') || entry.name.includes('reflow')) {
                    this.metrics.renderingMetrics.layoutTime += entry.duration;
                }
            }
        });
        
        try {
            measureObserver.observe({ entryTypes: ['measure'] });
            this.observers.push(measureObserver);
        } catch (e) {
            // 某些浏览器可能不支持 measure 观察
            console.warn('无法观察渲染指标:', e);
        }
    }
    
    /**
     * @function initUserInteractionMonitoring - 初始化用户交互监控
     */
    initUserInteractionMonitoring() {
        // 监控点击延迟
        document.addEventListener('click', (event) => {
            this.measureInteractionLatency('click', event);
        }, true);
        
        // 监控滚动性能
        let scrollStartTime = null;
        document.addEventListener('scroll', () => {
            if (!scrollStartTime) {
                scrollStartTime = performance.now();
                
                requestAnimationFrame(() => {
                    const scrollLatency = performance.now() - scrollStartTime;
                    this.metrics.userInteractionMetrics.scrollLatency.push(scrollLatency);
                    this.keepLatencyHistory('scroll');
                    scrollStartTime = null;
                });
            }
        }, { passive: true });
        
        // 监控输入延迟
        document.addEventListener('input', (event) => {
            this.measureInteractionLatency('input', event);
        }, true);
    }
    
    /**
     * @function measureInteractionLatency - 测量交互延迟
     * @param {string} type - 交互类型
     * @param {Event} event - 事件对象
     */
    measureInteractionLatency(type, event) {
        const startTime = performance.now();
        
        requestAnimationFrame(() => {
            const latency = performance.now() - startTime;
            const metrics = this.metrics.userInteractionMetrics;
            
            metrics[`${type}Latency`].push(latency);
            metrics.totalInteractions++;
            
            this.keepLatencyHistory(type);
            
            // 交互延迟警告
            if (latency > 100) {
                window.logger?.warn('交互监控', `${type}响应缓慢`, {
                    latency: `${latency.toFixed(2)}ms`,
                    target: event.target.tagName,
                    recommendation: '优化事件处理逻辑'
                });
            }
        });
    }
    
    /**
     * @function keepLatencyHistory - 保持延迟历史记录
     * @param {string} type - 交互类型
     */
    keepLatencyHistory(type) {
        const latencyArray = this.metrics.userInteractionMetrics[`${type}Latency`];
        if (latencyArray.length > 50) {
            latencyArray.shift();
        }
    }
    
    /**
     * @function startPeriodicReporting - 启动定期报告
     */
    startPeriodicReporting() {
        // 每30秒生成一次性能报告
        this.reportInterval = setInterval(() => {
            this.generatePeriodicReport();
        }, 30000);
    }
    
    /**
     * @function generatePeriodicReport - 生成定期报告
     */
        generatePeriodicReport() {
            if (!this.isMonitoring || !window.logger) return;
            
            const report = this.generateAdvancedReport();
            
            // 使用logger的新性能日志方法
            window.logger.logPerformanceMetrics(report);
            
            // 同时保留原有的info日志作为汇总
            window.logger.info('性能监控', '定期性能报告', {
                timestamp: new Date().toISOString(),
                uptime: `${((performance.now() - this.startTime) / 1000 / 60).toFixed(1)}分钟`,
                summary: {
                    overallScore: report.performanceScores ? report.performanceScores.overall : 'N/A',
                    memoryUsage: report.memoryMetrics ? report.memoryMetrics.usagePercent : 'N/A',
                    apiRequests: report.apiMetrics.totalRequests,
                    frameRate: report.renderingMetrics.frameRate
                }
            });
        }

    
    /**
     * @function generateAdvancedReport - 生成高级性能报告
     * @returns {object} 详细性能报告
     */
    generateAdvancedReport() {
        const scores = this.getPerformanceScore();
        
        return {
            coreMetrics: {
                fcp: `${this.metrics.firstContentfulPaint.toFixed(2)}ms`,
                lcp: `${this.metrics.largestContentfulPaint.toFixed(2)}ms`,
                fid: `${this.metrics.firstInputDelay.toFixed(2)}ms`,
                cls: this.metrics.cumulativeLayoutShift.toFixed(3)
            },
            memoryMetrics: this.metrics.memoryUsage ? {
                current: `${(this.metrics.memoryUsage.used / 1024 / 1024).toFixed(2)}MB`,
                peak: `${(this.metrics.realTimeMemory.peak / 1024 / 1024).toFixed(2)}MB`,
                usagePercent: `${this.metrics.memoryUsage.usagePercent}%`,
                limit: `${(this.metrics.memoryUsage.limit / 1024 / 1024).toFixed(2)}MB`
            } : null,
            apiMetrics: {
                totalRequests: this.metrics.apiMetrics.totalRequests,
                avgLatency: `${this.metrics.apiMetrics.averageLatency.toFixed(2)}ms`,
                errorRate: `${this.metrics.apiMetrics.errorRate}%`,
                minLatency: `${this.metrics.apiMetrics.minLatency.toFixed(2)}ms`,
                maxLatency: `${this.metrics.apiMetrics.maxLatency.toFixed(2)}ms`
            },
            renderingMetrics: {
                frameRate: `${this.metrics.renderingMetrics.frameRate.toFixed(1)}fps`,
                avgFrameTime: `${this.metrics.renderingMetrics.averageFrameTime.toFixed(2)}ms`,
                droppedFrames: this.metrics.renderingMetrics.droppedFrames,
                renderCalls: this.metrics.renderingMetrics.renderCalls
            },
            interactionMetrics: {
                totalInteractions: this.metrics.userInteractionMetrics.totalInteractions,
                avgClickLatency: this.getAverageLatency('click'),
                avgScrollLatency: this.getAverageLatency('scroll'),
                avgInputLatency: this.getAverageLatency('input')
            },
            performanceScores: scores,
            recommendations: this.getAdvancedRecommendations(scores)
        };
    }
    
    /**
     * @function getAverageLatency - 获取平均延迟
     * @param {string} type - 交互类型
     * @returns {string} 平均延迟字符串
     */
    getAverageLatency(type) {
        const latencies = this.metrics.userInteractionMetrics[`${type}Latency`];
        if (latencies.length === 0) return '0ms';
        
        const avg = latencies.reduce((sum, latency) => sum + latency, 0) / latencies.length;
        return `${avg.toFixed(2)}ms`;
    }
    
    /**
     * @function getAdvancedRecommendations - 获取高级优化建议
     * @param {object} scores - 性能评分
     * @returns {array} 优化建议列表
     */
    getAdvancedRecommendations(scores) {
        const recommendations = [];
        
        // 原有建议
        if (scores.fcp < 75) {
            recommendations.push('优化首次内容绘制：减少关键资源大小，使用内联CSS');
        }
        
        if (scores.lcp < 75) {
            recommendations.push('优化最大内容绘制：优化图片加载，使用预加载');
        }
        
        if (scores.fid < 75) {
            recommendations.push('优化首次输入延迟：减少JavaScript执行时间，使用代码分割');
        }
        
        if (scores.cls < 75) {
            recommendations.push('优化累积布局偏移：为图片设置尺寸，避免动态内容插入');
        }
        
        // 新增建议
        if (this.metrics.memoryUsage && parseFloat(this.metrics.memoryUsage.usagePercent) > 75) {
            recommendations.push('内存使用率过高：释放不必要的对象引用，优化缓存策略');
        }
        
        if (this.metrics.apiMetrics.averageLatency > 1000) {
            recommendations.push('API响应较慢：考虑使用缓存、CDN或优化后端性能');
        }
        
        if (this.metrics.apiMetrics.errorRate > 5) {
            recommendations.push('API错误率较高：检查网络稳定性和错误处理逻辑');
        }
        
        if (this.metrics.renderingMetrics.frameRate < 55) {
            recommendations.push('帧率偏低：优化渲染逻辑，减少DOM操作频率');
        }
        
        if (this.metrics.renderingMetrics.droppedFrames > 10) {
            recommendations.push('掉帧较多：检查动画性能，使用CSS3硬件加速');
        }
        
        return recommendations;
    }
    
    /**
     * @function pauseMonitoring - 暂停监控
     */
    pauseMonitoring() {
        this.isMonitoring = false;
        
        if (this.animationFrameId) {
            cancelAnimationFrame(this.animationFrameId);
        }
        
        if (this.memoryMonitorInterval) {
            clearInterval(this.memoryMonitorInterval);
        }
        
        if (this.reportInterval) {
            clearInterval(this.reportInterval);
        }
        
        window.logger?.info('性能监控', '监控已暂停（页面不可见）');
    }
    
    /**
     * @function resumeMonitoring - 恢复监控
     */
    resumeMonitoring() {
        this.isMonitoring = true;
        
        // 重新启动各种监控
        this.monitorFrameRate();
        this.initMemoryMonitoring();
        this.startPeriodicReporting();
        
        window.logger?.info('性能监控', '监控已恢复');
    }
    
    /**
     * @function generateFinalReport - 生成最终报告
     */
        generateFinalReport() {
            const finalReport = this.generateAdvancedReport();
            
            console.group('🚀 最终性能监控报告');
            console.log('📊 完整指标:', finalReport);
            console.groupEnd();
            
            if (window.logger) {
                // 使用logger的性能日志方法记录最终报告
                window.logger.logPerformanceMetrics(finalReport);
                
                // 记录会话总结
                window.logger.success('性能监控', '会话结束 - 最终性能报告', {
                    sessionDuration: `${((performance.now() - this.startTime) / 1000 / 60).toFixed(1)}分钟`,
                    totalApiRequests: finalReport.apiMetrics.totalRequests,
                    totalInteractions: finalReport.interactionMetrics.totalInteractions,
                    overallPerformanceScore: finalReport.performanceScores ? finalReport.performanceScores.overall : 'N/A',
                    memoryPeakUsage: finalReport.memoryMetrics ? finalReport.memoryMetrics.peak : 'N/A',
                    mainRecommendations: finalReport.recommendations.slice(0, 3) // 前3条主要建议
                });
            }
            
            return finalReport;
        }

    
    // 保留原有方法...
    observeNavigationTiming() {
        window.addEventListener('DOMContentLoaded', () => {
            const navigation = performance.getEntriesByType('navigation')[0];
            if (navigation) {
                this.metrics.domContentLoaded = navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart;
                this.metrics.loadTime = navigation.loadEventEnd - navigation.loadEventStart;
            }
        });
        
        window.addEventListener('load', () => {
            setTimeout(() => this.calculateLoadMetrics(), 0);
        });
    }
    
    observePaintTiming() {
        if ('PerformanceObserver' in window) {
            const paintObserver = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    if (entry.name === 'first-paint') {
                        this.metrics.firstPaint = entry.startTime;
                    } else if (entry.name === 'first-contentful-paint') {
                        this.metrics.firstContentfulPaint = entry.startTime;
                    }
                }
            });
            
            paintObserver.observe({ entryTypes: ['paint'] });
            this.observers.push(paintObserver);
        }
    }
    
    observeLargestContentfulPaint() {
        if ('PerformanceObserver' in window) {
            const lcpObserver = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                const lastEntry = entries[entries.length - 1];
                this.metrics.largestContentfulPaint = lastEntry.startTime;
            });
            
            lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
            this.observers.push(lcpObserver);
        }
    }
    
    observeFirstInputDelay() {
        if ('PerformanceObserver' in window) {
            const fidObserver = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    this.metrics.firstInputDelay = entry.processingStart - entry.startTime;
                    break;
                }
            });
            
            fidObserver.observe({ entryTypes: ['first-input'] });
            this.observers.push(fidObserver);
        }
    }
    
    observeCumulativeLayoutShift() {
        if ('PerformanceObserver' in window) {
            let clsValue = 0;
            const clsObserver = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    if (!entry.hadRecentInput) {
                        clsValue += entry.value;
                    }
                }
                this.metrics.cumulativeLayoutShift = clsValue;
            });
            
            clsObserver.observe({ entryTypes: ['layout-shift'] });
            this.observers.push(clsObserver);
        }
    }
    
    observeResourceTiming() {
        if ('PerformanceObserver' in window) {
            const resourceObserver = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    const loadTime = entry.responseEnd - entry.startTime;
                    this.metrics.resourceLoadTimes.set(entry.name, {
                        loadTime,
                        size: entry.transferSize || 0,
                        type: this.getResourceType(entry.name)
                    });
                }
            });
            
            resourceObserver.observe({ entryTypes: ['resource'] });
            this.observers.push(resourceObserver);
        }
    }
    
    getResourceType(url) {
        if (url.includes('.css')) return 'CSS';
        if (url.includes('.js')) return 'JavaScript';
        if (url.match(/\.(jpg|jpeg|png|gif|webp|svg)$/i)) return 'Image';
        if (url.includes('font')) return 'Font';
        return 'Other';
    }
    
    calculateLoadMetrics() {
        const navigation = performance.getEntriesByType('navigation')[0];
        if (navigation) {
            this.metrics.loadTime = navigation.loadEventEnd - navigation.fetchStart;
        }
    }
    
    getPerformanceScore() {
        const scores = {
            fcp: this.scoreFCP(this.metrics.firstContentfulPaint),
            lcp: this.scoreLCP(this.metrics.largestContentfulPaint),
            fid: this.scoreFID(this.metrics.firstInputDelay),
            cls: this.scoreCLS(this.metrics.cumulativeLayoutShift)
        };
        
        const overall = Math.round((scores.fcp + scores.lcp + scores.fid + scores.cls) / 4);
        
        return { ...scores, overall };
    }
    
    scoreFCP(fcp) {
        if (fcp <= 1800) return 100;
        if (fcp <= 3000) return Math.round(100 - ((fcp - 1800) / 1200) * 50);
        return Math.max(0, Math.round(50 - ((fcp - 3000) / 2000) * 50));
    }
    
    scoreLCP(lcp) {
        if (lcp <= 2500) return 100;
        if (lcp <= 4000) return Math.round(100 - ((lcp - 2500) / 1500) * 50);
        return Math.max(0, Math.round(50 - ((lcp - 4000) / 2000) * 50));
    }
    
    scoreFID(fid) {
        if (fid <= 100) return 100;
        if (fid <= 300) return Math.round(100 - ((fid - 100) / 200) * 50);
        return Math.max(0, Math.round(50 - ((fid - 300) / 200) * 50));
    }
    
    scoreCLS(cls) {
        if (cls <= 0.1) return 100;
        if (cls <= 0.25) return Math.round(100 - ((cls - 0.1) / 0.15) * 50);
        return Math.max(0, Math.round(50 - ((cls - 0.25) / 0.25) * 50));
    }
    
    generateReport() {
        return this.generateAdvancedReport();
    }
    
    getRecommendations(scores) {
        return this.getAdvancedRecommendations(scores);
    }
    
        destroy() {
            this.isMonitoring = false;
            
            this.observers.forEach(observer => observer.disconnect());
            this.observers = [];
            
            if (this.animationFrameId) {
                cancelAnimationFrame(this.animationFrameId);
            }
            
            if (this.memoryMonitorInterval) {
                clearInterval(this.memoryMonitorInterval);
            }
            
            if (this.reportInterval) {
                clearInterval(this.reportInterval);
            }
            
            // 停止logger的性能日志记录
            if (window.logger && typeof window.logger.stopPerformanceLogging === 'function') {
                window.logger.stopPerformanceLogging();
            }
            
            // 生成最终报告
            this.generateFinalReport();
            
            if (window.logger) {
                window.logger.info('性能监控', '性能监控器已销毁，所有监控已停止');
            }
        }

}

// 全局性能监控器
window.performanceMonitor = new PerformanceMonitor();

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PerformanceMonitor;
}
