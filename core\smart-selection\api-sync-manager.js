/**
 * @file api-sync-manager.js - 动态API数据同步管理器
 * @description 实现与GoMyHire API的实时数据同步，自动更新车型、服务类型、用户数据
 * <AUTHOR> IDE
 * @created_at 2025-01-06
 * @updated_at 2025-01-06
 */

/**
 * @class DynamicApiSyncManager - 动态API数据同步管理器
 * @description 实现与GoMyHire API的实时数据同步，自动更新车型、服务类型、用户数据
 */
class DynamicApiSyncManager {
    constructor() {
            this.syncInterval = 30 * 60 * 1000; // 30分钟同步一次
            this.lastSyncTime = null;
            this.syncStatus = 'idle';
            
            // 从DI容器获取ErrorHandler（如果可用）
            this.errorHandler = window.resolveService ? window.resolveService('errorHandler') : null;
            
            if (this.errorHandler) {
                // 使用统一重试机制
                this.dataSyncRetry = this.errorHandler.createRetryWrapper(
                    this._performDataSync.bind(this),
                    {
                        maxAttempts: 3,
                        baseDelay: 30000, // 30秒延迟
                        maxDelay: 120000, // 最大2分钟
                        backoffMultiplier: 2,
                        retryableErrors: ['network', 'timeout', 'server_error', '503', '502']
                    },
                    {
                        source: 'DynamicApiSyncManager',
                        operation: 'syncData',
                        beforeRetry: this._beforeSyncRetry.bind(this)
                    }
                );
                logger.success('DynamicApiSyncManager', '已集成统一重试机制');
            } else {
                // 降级到传统重试机制
                this.retryCount = 0;
                this.maxRetries = 3;
                logger.warn('DynamicApiSyncManager', '使用传统重试机制');
            }
            
            this.autoSyncTimer = null;
            this.statusListeners = [];
            
            this.setupLoginStatusListener();
        }

    
    /**
     * @function startAutoSync - 启动自动同步机制
     */
    startAutoSync() {
        // 首次同步
        this.syncAllData();
        
        // 定时同步
        this.syncTimer = setInterval(() => {
            this.syncAllData();
        }, this.syncInterval);
        
        // 监听用户登录状态变化
        this.setupLoginStatusListener();
        
        console.log('DynamicApiSync', '自动同步机制已启动', {
            interval: this.syncInterval,
            endpoints: Object.keys(this.apiEndpoints)
        });
    }
    
    /**
     * @function setupLoginStatusListener - 设置登录状态监听器
     * @description 监听用户登录状态变化，在用户登录后立即同步数据
     */
    setupLoginStatusListener() {
        const app = window.app;
        if (app && app.appState && app.appState.subscribe) {
            // 监听用户登录事件
            app.appState.subscribe('userLogin', () => {
                console.log('DynamicApiSync', '检测到用户登录，立即执行数据同步');
                this.syncAllData();
            });
            
            // 监听用户登出事件
            app.appState.subscribe('userLogout', () => {
                console.log('DynamicApiSync', '检测到用户登出，停止数据同步');
                this.syncStatus = 'waiting_login';
            });
        }
    }
    
    /**
     * @function syncAllData - 同步所有API数据
     * @returns {Promise<Object>} 同步结果
     */
    async syncAllData() {
            try {
                // 检查用户登录状态
                const app = window.app;
                if (!app || !app.appState || !app.appState.token) {
                    console.log('DynamicApiSync', '用户未登录，跳过API数据同步');
                    this.syncStatus = 'waiting_login';
                    this.notifyStatusChange('sync_skipped', {
                        reason: '用户未登录'
                    });
                    return {
                        success: false,
                        error: '用户未登录，无法同步数据',
                        skipReason: 'not_logged_in'
                    };
                }
                
                this.syncStatus = 'syncing';
                this.notifyStatusChange('sync_started');
                
                console.log('DynamicApiSync', '开始API数据同步');
                
                let result;
                
                if (this.errorHandler && this.dataSyncRetry) {
                    // 使用统一重试机制
                    console.log('DynamicApiSync', '使用统一重试机制进行数据同步');
                    result = await this.dataSyncRetry();
                } else {
                    // 降级到传统重试机制
                    console.warn('DynamicApiSync', '降级到传统重试机制');
                    result = await this._legacyDataSync();
                }
                
                return result;
                
            } catch (error) {
                console.error('DynamicApiSync', 'API数据同步失败', error);
                
                this.syncStatus = 'error';
                
                this.notifyStatusChange('sync_error', {
                    error: error.message,
                    usesUnifiedRetry: !!this.errorHandler
                });
                
                // 如果使用传统机制且需要重试
                if (!this.errorHandler) {
                    this.retryCount++;
                    if (this.retryCount < this.maxRetries) {
                        console.log('DynamicApiSync', `将于30秒后进行第${this.retryCount + 1}次重试`);
                        setTimeout(() => this.syncAllData(), 30000);
                    }
                }
                
                return {
                    success: false,
                    error: error.message,
                    retryCount: this.retryCount || 0
                };
            }
        }
    
        /**
         * @function _performDataSync - 内部数据同步操作（供重试机制使用）
         * @description 实际的数据同步操作，由ErrorHandler重试机制包装
         * @private
         */
        async _performDataSync() {
            // 并行获取所有数据
            const [backendUsers, subCategories, carTypes] = await Promise.all([
                this.fetchBackendUsers(),
                this.fetchSubCategories(),
                this.fetchCarTypes()
            ]);
            
            // 验证数据完整性
            const validationResult = this.validateSyncedData({
                backendUsers,
                subCategories, 
                carTypes
            });
            
            if (!validationResult.valid) {
                throw new Error(`数据验证失败: ${validationResult.errors.join(', ')}`);
            }
            
            // 更新应用状态
            this.updateAppState({
                backendUsers,
                subCategories,
                carTypes
            });
            
            this.lastSyncTime = Date.now();
            this.syncStatus = 'success';
            if (this.retryCount) this.retryCount = 0; // 重置传统重试计数
            
            console.log('DynamicApiSync', 'API数据同步成功', {
                backendUsers: backendUsers.length,
                subCategories: subCategories.length,
                carTypes: carTypes.length,
                timestamp: new Date().toISOString()
            });
            
            this.notifyStatusChange('sync_success', {
                backendUsers: backendUsers.length,
                subCategories: subCategories.length,
                carTypes: carTypes.length
            });
            
            return {
                success: true,
                data: { backendUsers, subCategories, carTypes },
                timestamp: this.lastSyncTime
            };
        }
    
        /**
         * @function _beforeSyncRetry - 数据同步重试前的预处理
         * @description 在每次重试前执行的清理和准备工作
         * @private
         */
        async _beforeSyncRetry(error, attempt) {
            console.log('DynamicApiSync', `数据同步重试前预处理 - 第${attempt}次`, {
                error: error.message
            });
            
            // 检查是否是认证相关错误
            if (error.message.includes('401') || error.message.includes('认证')) {
                console.log('DynamicApiSync', '检测到认证错误，尝试刷新token');
                
                // 通知认证错误，可能需要重新登录
                this.notifyStatusChange('auth_error', {
                    attempt: attempt,
                    error: error.message
                });
            }
            
            // 通知重试状态
            this.notifyStatusChange('sync_retrying', {
                attempt: attempt,
                error: error.message
            });
        }
    
        /**
         * @function _legacyDataSync - 传统数据同步机制（降级使用）
         * @description 当ErrorHandler不可用时的降级同步方法
         * @private
         */
        async _legacyDataSync() {
            // 这里直接调用原有的同步逻辑
            const [backendUsers, subCategories, carTypes] = await Promise.all([
                this.fetchBackendUsers(),
                this.fetchSubCategories(),
                this.fetchCarTypes()
            ]);
            
            const validationResult = this.validateSyncedData({
                backendUsers,
                subCategories, 
                carTypes
            });
            
            if (!validationResult.valid) {
                throw new Error(`数据验证失败: ${validationResult.errors.join(', ')}`);
            }
            
            this.updateAppState({
                backendUsers,
                subCategories,
                carTypes
            });
            
            this.lastSyncTime = Date.now();
            this.syncStatus = 'success';
            this.retryCount = 0;
            
            console.log('DynamicApiSync', 'API数据同步成功（传统机制）', {
                backendUsers: backendUsers.length,
                subCategories: subCategories.length,
                carTypes: carTypes.length
            });
            
            this.notifyStatusChange('sync_success', {
                backendUsers: backendUsers.length,
                subCategories: subCategories.length,
                carTypes: carTypes.length
            });
            
            return {
                success: true,
                data: { backendUsers, subCategories, carTypes },
                timestamp: this.lastSyncTime
            };
        }
    
    /**
     * @function fetchBackendUsers - 获取后台用户数据
     * @returns {Promise<Array>} 用户数据
     */
    async fetchBackendUsers() {
        try {
            // 检查应用状态中是否已有数据
            const app = window.app;
            if (app && app.appState && app.appState.backendUsers) {
                console.log('DynamicApiSync', '使用应用状态中的后台用户数据');
                return app.appState.backendUsers;
            }
            
            // 模拟API调用 - 实际项目中替换为真实API
            console.log('DynamicApiSync', '模拟获取后台用户数据');
            
            // 基于gomyhire api id.md中的真实数据
            const mockUsers = [
                { "id": 1, "name": "Super Admin", "phone": "0162234711", "role": "Super Admin" },
                { "id": 105, "name": "Mei Kwan", "phone": null, "role": "Super Admin" },
                { "id": 22, "name": "Zahidah1", "phone": "0132456789", "role": "Operator" },
                { "id": 106, "name": "admin", "phone": null, "role": "Operator" },
                { "id": 108, "name": "meikwan", "phone": "0123456789", "role": "Operator" },
                { "id": 143, "name": "Kk", "phone": "012", "role": "Operator" },
                { "id": 206, "name": "OperatorRinglee", "phone": "0181122334", "role": "Operator" },
                { "id": 362, "name": "Kcy", "phone": null, "role": "Operator" },
                { "id": 110, "name": "Sub Admin", "phone": "0162234711", "role": "Sub_Admin" },
                { "id": 229, "name": "test", "phone": null, "role": "Sub_Admin" },
                { "id": 338, "name": "jcy1", "phone": null, "role": "Sub_Admin" },
                { "id": 163, "name": "Kok1", "phone": null, "role": "Sub_Operator" }
            ];
            
            return mockUsers;
            
        } catch (error) {
            console.error('DynamicApiSync', '获取后台用户数据失败', error);
            throw error;
        }
    }
    
    /**
     * @function fetchSubCategories - 获取子分类数据
     * @returns {Promise<Array>} 子分类数据
     */
    async fetchSubCategories() {
        try {
            const app = window.app;
            if (app && app.appState && app.appState.subCategories) {
                console.log('DynamicApiSync', '使用应用状态中的子分类数据');
                return app.appState.subCategories;
            }
            
            console.log('DynamicApiSync', '模拟获取子分类数据');
            
            // 基于gomyhire api id.md中的真实数据
            const mockSubCategories = [
                {
                    "id": 7,
                    "main_category": "Airport",
                    "name": "Pickup",
                    "preset_data": {
                        "order_type": "pickup",
                        "ota": null,
                        "driving_region": "KL - RM - Kuala Lumpur",
                        "languages": ["JP - Japanese", "MY - Malay", "CN - Chinese", "EN - English", "KR - Korean", "TML - Tamil"],
                        "extra_requirement": "Pickup at the right time please!"
                    },
                    "required_fields": ["Customer Name", "Customer Contact", "Driving Region", "Pickup Address", "Pickup Date", "Pickup Time", "Destination Address", "Passenger Number", "Driver Fee"]
                },
                {
                    "id": 8,
                    "main_category": "Airport",
                    "name": "Dropoff",
                    "preset_data": {
                        "order_type": "dropoff",
                        "ota": null,
                        "driving_region": "KL - RM - Kuala Lumpur",
                        "languages": ["JP - Japanese", "MY - Malay", "CN - Chinese", "EN - English"],
                        "extra_requirement": null
                    },
                    "required_fields": ["Customer Name", "Customer Contact", "Driving Region", "Pickup Address", "Pickup Date", "Pickup Time", "Destination Address", "Passenger Number", "Driver Fee"]
                },
                {
                    "id": 9,
                    "main_category": "Chartered",
                    "name": "KL to genting",
                    "preset_data": {
                        "order_type": "charter",
                        "ota": null,
                        "driving_region": null,
                        "languages": ["EN - English"],
                        "extra_requirement": "asdafdsgghfdfgdjhfygfcvxfgdtgcbncbncghfgfhcvbncvb..."
                    },
                    "required_fields": ["Customer Name", "Customer Contact", "Driving Region", "Pickup Address", "Pickup Date", "Pickup Time", "Destination Address", "Passenger Number", "Driver Fee"]
                },
                {
                    "id": 43,
                    "main_category": "Chartered",
                    "name": "Charter",
                    "preset_data": {
                        "order_type": "charter",
                        "ota": null,
                        "driving_region": null,
                        "languages": [],
                        "extra_requirement": null
                    },
                    "required_fields": ["Customer Name", "Customer Contact", "Driving Region", "Pickup Address", "Pickup Date", "Pickup Time", "Destination Address", "Passenger Number", "Driver Fee"]
                }
            ];
            
            return mockSubCategories;
            
        } catch (error) {
            console.error('DynamicApiSync', '获取子分类数据失败', error);
            throw error;
        }
    }
    
    /**
     * @function fetchCarTypes - 获取车型数据
     * @returns {Promise<Array>} 车型数据
     */
    async fetchCarTypes() {
        try {
            const app = window.app;
            if (app && app.appState && app.appState.carTypes) {
                console.log('DynamicApiSync', '使用应用状态中的车型数据');
                return app.appState.carTypes;
            }
            
            console.log('DynamicApiSync', '模拟获取车型数据');
            
            // 基于gomyhire api id.md中的真实数据
            const mockCarTypes = [
                { "id": 5, "type": "Compact 5 Seater", "seat_number": 4, "priority": 1 },
                { "id": 6, "type": "Comfort 5 Seater", "seat_number": 4, "priority": 2 },
                { "id": 15, "type": "Mid Size SUV", "seat_number": 7, "priority": 3 },
                { "id": 16, "type": "Standard Size MPV", "seat_number": 6, "priority": 4 },
                { "id": 31, "type": "Luxury Mpv", "seat_number": 6, "priority": 5 },
                { "id": 32, "type": "Alphard/Velfire", "seat_number": 6, "priority": 6 },
                { "id": 20, "type": "10 Seater MPV / Van", "seat_number": 9, "priority": 7 },
                { "id": 30, "type": "12 Seater MPV", "seat_number": 11, "priority": 8 },
                { "id": 23, "type": "14 Seater Van", "seat_number": 12, "priority": 9 },
                { "id": 24, "type": "18 Seater Van", "seat_number": 16, "priority": 10 },
                { "id": 25, "type": "30 Seat Mini Bus", "seat_number": 30, "priority": 11 },
                { "id": 26, "type": "44 Seater Bus", "seat_number": 44, "priority": 12 },
                { "id": 34, "type": "Please Refer Live Chat", "seat_number": 1, "priority": 13 }
            ];
            
            return mockCarTypes;
            
        } catch (error) {
            console.error('DynamicApiSync', '获取车型数据失败', error);
            throw error;
        }
    }

    /**
     * @function validateSyncedData - 验证同步的数据
     * @param {Object} data - 同步的数据
     * @returns {Object} 验证结果
     */
    validateSyncedData(data) {
        const errors = [];

        // 验证后台用户数据
        if (!Array.isArray(data.backendUsers) || data.backendUsers.length === 0) {
            errors.push('后台用户数据无效或为空');
        } else {
            const invalidUsers = data.backendUsers.filter(user => !user.id || !user.name);
            if (invalidUsers.length > 0) {
                errors.push(`发现${invalidUsers.length}个无效用户记录`);
            }
        }

        // 验证子分类数据
        if (!Array.isArray(data.subCategories) || data.subCategories.length === 0) {
            errors.push('子分类数据无效或为空');
        } else {
            const invalidCategories = data.subCategories.filter(cat => !cat.id || !cat.name);
            if (invalidCategories.length > 0) {
                errors.push(`发现${invalidCategories.length}个无效分类记录`);
            }
        }

        // 验证车型数据
        if (!Array.isArray(data.carTypes) || data.carTypes.length === 0) {
            errors.push('车型数据无效或为空');
        } else {
            const invalidCarTypes = data.carTypes.filter(car => !car.id || !car.type);
            if (invalidCarTypes.length > 0) {
                errors.push(`发现${invalidCarTypes.length}个无效车型记录`);
            }
        }

        return {
            valid: errors.length === 0,
            errors: errors
        };
    }

    /**
     * @function updateAppState - 更新应用状态
     * @param {Object} data - 新数据
     */
    updateAppState(data) {
        try {
            const app = window.app;
            if (app && app.appState) {
                // 更新应用状态
                app.appState.backendUsers = data.backendUsers;
                app.appState.subCategories = data.subCategories;
                app.appState.carTypes = data.carTypes;

                // 触发智能选择服务更新映射表
                if (window.smartSelection) {
                    window.smartSelection.updateMappingFromAppState();
                }

                console.log('DynamicApiSync', '应用状态已更新', {
                    backendUsersCount: data.backendUsers.length,
                    subCategoriesCount: data.subCategories.length,
                    carTypesCount: data.carTypes.length
                });
            }
        } catch (error) {
            console.error('DynamicApiSync', '更新应用状态失败', error);
        }
    }

    /**
     * @function addStatusListener - 添加状态监听器
     * @param {Function} listener - 监听器函数
     */
    addStatusListener(listener) {
        this.statusListeners.push(listener);
    }

    /**
     * @function notifyStatusChange - 通知状态变更
     * @param {string} status - 状态
     * @param {Object} data - 附加数据
     */
    notifyStatusChange(status, data = {}) {
        this.statusListeners.forEach(listener => {
            try {
                listener(status, data);
            } catch (error) {
                console.error('DynamicApiSync', '状态监听器执行失败', error);
            }
        });
    }

    /**
     * @function forceSync - 强制立即同步
     * @returns {Promise<Object>} 同步结果
     */
    async forceSync() {
        console.log('DynamicApiSync', '强制执行数据同步');
        return await this.syncAllData();
    }

    /**
     * @function getSyncStatus - 获取同步状态
     * @returns {Object} 同步状态信息
     */
    getSyncStatus() {
        return {
            status: this.syncStatus,
            lastSyncTime: this.lastSyncTime,
            retryCount: this.retryCount,
            nextSyncTime: this.lastSyncTime ? this.lastSyncTime + this.syncInterval : null
        };
    }

    /**
     * @function stopAutoSync - 停止自动同步
     */
    stopAutoSync() {
        if (this.syncTimer) {
            clearInterval(this.syncTimer);
            this.syncTimer = null;
            console.log('DynamicApiSync', '自动同步已停止');
        }
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DynamicApiSyncManager;
} else if (typeof window !== 'undefined') {
    window.DynamicApiSyncManager = DynamicApiSyncManager;
}
